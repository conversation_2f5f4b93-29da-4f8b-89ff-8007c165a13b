<template>
  <el-dialog
    v-model="dialogVisible"
    width="1200px"
    :before-close="handleClose"
    :show-close="false"
    class="modern-dialog"
  >
    <!-- 自定义标题 -->
    <template #header>
      <div class="flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
          <div>
            <h3 class="text-xl font-bold text-gray-900 dark:text-dark-text">裂变详情</h3>
            <p class="text-sm text-gray-600 dark:text-dark-text-secondary">任务ID: {{ task?.id || '' }}</p>
          </div>
        </div>
        <button @click="handleClose"
          class="p-2 text-gray-400 hover:text-gray-600 dark:text-dark-text-secondary dark:hover:text-dark-text rounded-lg hover:bg-gray-100 dark:hover:bg-dark-card transition-all duration-200">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </template>

    <!-- 裂变信息概览 -->
    <div v-if="task" class="p-6 grid grid-cols-2 md:grid-cols-4 gap-4">
      <div class="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-4 rounded-xl border border-purple-200 dark:border-purple-800">
        <div class="flex items-center space-x-2">
          <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div>
            <p class="text-xs text-purple-600 dark:text-purple-400 font-medium">任务状态</p>
            <p class="text-sm font-bold text-purple-900 dark:text-purple-100">{{ getStatusText(task.status) }}</p>
          </div>
        </div>
      </div>

      <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-4 rounded-xl border border-blue-200 dark:border-blue-800">
        <div class="flex items-center space-x-2">
          <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
          </div>
          <div>
            <p class="text-xs text-blue-600 dark:text-blue-400 font-medium">原图数量</p>
            <p class="text-sm font-bold text-blue-900 dark:text-blue-100">{{ task.targetCount }}</p>
          </div>
        </div>
      </div>

      <div class="bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-900/20 dark:to-indigo-800/20 p-4 rounded-xl border border-indigo-200 dark:border-indigo-800">
        <div class="flex items-center space-x-2">
          <div class="w-8 h-8 bg-indigo-500 rounded-lg flex items-center justify-center">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
          <div>
            <p class="text-xs text-indigo-600 dark:text-indigo-400 font-medium">裂变数量</p>
            <p class="text-sm font-bold text-indigo-900 dark:text-indigo-100">{{ task.splitCount }}</p>
          </div>
        </div>
      </div>

      <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-4 rounded-xl border border-green-200 dark:border-green-800">
        <div class="flex items-center space-x-2">
          <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <div>
            <p class="text-xs text-green-600 dark:text-green-400 font-medium">成功生成</p>
            <p class="text-sm font-bold text-green-900 dark:text-green-100">{{ task.successCount }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 裂变结果列表 -->
    <div class="px-6 pb-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-dark-text mb-4">裂变结果</h3>
      <div class="bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden">
        <el-table
          :data="processResults"
          style="width: 100%"
          v-loading="loading"
          max-height="400"
          class="modern-table"
        >
          <el-table-column prop="index" label="序号" width="80" align="center" />
          <el-table-column label="原图" width="100" align="center">
            <template #default="scope">
              <div v-if="scope.row.status === 'success'" class="flex justify-center">
                <el-image
                  :src="scope.row.originalImage"
                  :preview-src-list="[scope.row.originalImage]"
                  fit="cover"
                  class="w-16 h-16 rounded-lg border border-gray-200 dark:border-dark-border"
                  :preview-teleported="true"
                />
              </div>
              <div v-else class="flex justify-center">
                <div class="w-16 h-16 bg-gray-100 dark:bg-dark-card rounded-lg flex items-center justify-center">
                  <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="裂变结果" width="400" align="center">
            <template #default="scope">
              <div v-if="scope.row.status === 'success'" class="flex justify-center space-x-2">
                <div v-for="(result, index) in scope.row.splitResults" :key="index" class="relative">
                  <el-image
                    :src="result.url"
                    :preview-src-list="scope.row.splitResults.map((r: any) => r.url)"
                    :initial-index="index"
                    fit="cover"
                    class="w-16 h-16 rounded-lg border border-gray-200 dark:border-dark-border"
                    :preview-teleported="true"
                  />
                  <div class="absolute -top-1 -right-1 w-4 h-4 bg-purple-500 text-white text-xs rounded-full flex items-center justify-center">
                    {{ index + 1 }}
                  </div>
                </div>
              </div>
              <div v-else class="flex justify-center">
                <span class="text-sm text-gray-500 dark:text-dark-text-secondary">{{ getItemStatusText(scope.row.status) }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="fileName" label="文件名" width="200" align="center">
            <template #default="scope">
              <span class="text-sm text-gray-600 dark:text-dark-text-secondary">{{ scope.row.fileName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="scope">
              <el-tag
                :type="scope.row.status === 'success' ? 'success' : 'danger'"
                size="small"
              >
                {{ getItemStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center">
            <template #default="scope">
              <div v-if="scope.row.status === 'success'" class="flex justify-center">
                <button @click="downloadResult(scope.row)"
                  class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-all duration-200">
                  下载
                </button>
              </div>
              <div v-else class="flex justify-center">
                <span class="text-sm text-gray-400">-</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <div class="flex items-center justify-between p-6 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50">
        <div class="text-sm text-gray-600 dark:text-dark-text-secondary">
          共 {{ total }} 条裂变结果
        </div>
        <div class="flex items-center space-x-3">
          <button @click="handleClose"
            class="px-6 py-2.5 text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200">
            关闭
          </button>
          <button @click="exportDetails"
            class="inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105">
            <ArrowDownTrayIcon class="w-5 h-5 mr-2" />
            导出详情
          </button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { ArrowDownTrayIcon } from '@heroicons/vue/24/outline';

// Props
const props = defineProps<{
  modelValue: boolean;
  task: any;
}>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
}>();

// 类型定义
interface ProcessResult {
  index: number;
  originalImage: string;
  splitResults: Array<{
    url: string;
    name: string;
  }>;
  fileName: string;
  status: 'success' | 'failed';
}

// 响应式数据
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const loading = ref(false);
const total = ref(0);

// 模拟裂变结果数据
const processResults = ref<ProcessResult[]>([
  {
    index: 1,
    originalImage: 'https://picsum.photos/400/400?random=1',
    splitResults: [
      { url: 'https://picsum.photos/400/400?random=11', name: 'split_1_v1.jpg' },
      { url: 'https://picsum.photos/400/400?random=12', name: 'split_1_v2.jpg' },
      { url: 'https://picsum.photos/400/400?random=13', name: 'split_1_v3.jpg' }
    ],
    fileName: 'product_001.jpg',
    status: 'success'
  },
  {
    index: 2,
    originalImage: 'https://picsum.photos/400/400?random=2',
    splitResults: [
      { url: 'https://picsum.photos/400/400?random=21', name: 'split_2_v1.jpg' },
      { url: 'https://picsum.photos/400/400?random=22', name: 'split_2_v2.jpg' },
      { url: 'https://picsum.photos/400/400?random=23', name: 'split_2_v3.jpg' },
      { url: 'https://picsum.photos/400/400?random=24', name: 'split_2_v4.jpg' },
      { url: 'https://picsum.photos/400/400?random=25', name: 'split_2_v5.jpg' }
    ],
    fileName: 'product_002.jpg',
    status: 'success'
  },
  {
    index: 3,
    originalImage: 'https://picsum.photos/400/400?random=3',
    splitResults: [],
    fileName: 'product_003.jpg',
    status: 'failed'
  }
]);

// 初始化数据
total.value = processResults.value.length;

// 状态相关方法
const getStatusText = (status: string) => {
  const statusTexts: Record<string, string> = {
    'completed': '已完成',
    'processing': '处理中',
    'failed': '失败',
    'pending': '等待中'
  };
  return statusTexts[status] || '未知';
};

// const getStatusType = (status: string) => {
//   const statusTypes: Record<string, string> = {
//     'completed': 'success',
//     'processing': 'warning',
//     'failed': 'danger',
//     'pending': 'info'
//   };
//   return statusTypes[status] || 'info';
// };

const getItemStatusText = (status: ProcessResult['status']) => {
  const statusTexts: Record<ProcessResult['status'], string> = {
    'success': '成功',
    'failed': '失败'
  };
  return statusTexts[status] || '未知';
};

// 操作方法
const handleClose = () => {
  dialogVisible.value = false;
};

const downloadResult = (item: ProcessResult) => {
  ElMessage.success(`正在下载 ${item.fileName} 的所有裂变结果`);
  // 这里应该实现实际的下载逻辑
};

const exportDetails = () => {
  ElMessage.success('正在导出详情...');
  // 这里应该实现导出逻辑
};
</script>

<style scoped>
.modern-dialog :deep(.el-dialog__header) {
  padding: 0;
  margin: 0;
}

.modern-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.modern-dialog :deep(.el-dialog__footer) {
  padding: 0;
}

.modern-table {
  width: 100% !important;
  --el-table-border-color: theme('colors.gray.100');
  --el-table-bg-color: theme('colors.white');
  --el-table-tr-bg-color: theme('colors.white');
  --el-table-expanded-cell-bg-color: theme('colors.gray.50');
}

.modern-table :deep(.el-table) {
  width: 100% !important;
}

.modern-table :deep(.el-table__header-wrapper) {
  background: transparent;
  width: 100% !important;
}

.modern-table :deep(.el-table__body-wrapper) {
  background: transparent;
  width: 100% !important;
}

.modern-table :deep(.el-table__row) {
  transition: all 0.2s ease;
}

.modern-table :deep(.el-table__row:hover) {
  background-color: rgba(147, 51, 234, 0.05) !important;
  transform: translateY(-1px);
}

.modern-table :deep(.el-table__header) {
  width: 100% !important;
}

.modern-table :deep(.el-table__body) {
  width: 100% !important;
}

.dark .modern-table {
  --el-table-border-color: theme('colors.gray.700');
  --el-table-bg-color: theme('colors.gray.800');
  --el-table-tr-bg-color: theme('colors.gray.800');
  --el-table-expanded-cell-bg-color: theme('colors.gray.700');
}
</style>
