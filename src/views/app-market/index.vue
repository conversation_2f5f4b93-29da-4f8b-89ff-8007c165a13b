<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 rounded-2xl p-6 border border-primary-100 dark:border-primary-800">
      <div class="flex items-center space-x-3">
        <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
          </svg>
        </div>
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text">应用市场</h1>
          <p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary">发现和管理您的应用工具</p>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div class="bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-dark-text-secondary">总应用数</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-dark-text">{{ totalApps }}</p>
          </div>
          <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900/30 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
            </svg>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-dark-text-secondary">我的收藏</p>
            <p class="text-2xl font-bold text-green-600 dark:text-green-400">{{ favoriteCount }}</p>
          </div>
          <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
            </svg>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-dark-text-secondary">已安装</p>
            <p class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ installedCount }}</p>
          </div>
          <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="bg-white dark:bg-dark-surface rounded-2xl p-8 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border">
      <!-- 搜索框 -->
      <div class="mb-6">
        <div class="relative max-w-2xl mx-auto">
          <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
            <MagnifyingGlassIcon class="w-5 h-5 text-gray-400" />
          </div>
          <input
            v-model="searchKeyword"
            @input="handleSearch"
            type="text"
            placeholder="搜索应用名称、描述或标签..."
            class="w-full pl-12 pr-4 py-4 text-lg bg-gray-50 dark:bg-dark-card border border-gray-200 dark:border-dark-border rounded-2xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 placeholder-gray-400 dark:placeholder-gray-500 text-gray-900 dark:text-dark-text"
          />
          <button
            v-if="searchKeyword"
            @click="searchKeyword = ''; handleSearch()"
            class="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- 筛选器 -->
      <div class="flex flex-wrap items-center justify-center gap-4">

        <!-- 分类筛选 -->
        <div class="filter-group">
          <label class="filter-label">分类</label>
          <select
            v-model="selectedCategory"
            @change="handleCategoryChange"
            class="filter-select"
          >
            <option value="">全部分类</option>
            <option
              v-for="category in categoryOptions"
              :key="category.value"
              :value="category.value"
            >
              {{ category.label }}
            </option>
          </select>
        </div>

        <!-- 价格类型筛选 -->
        <div class="filter-group">
          <label class="filter-label">价格</label>
          <select
            v-model="selectedPriceType"
            @change="handlePriceTypeChange"
            class="filter-select"
          >
            <option value="">全部价格</option>
            <option
              v-for="priceType in priceTypeOptions"
              :key="priceType.value"
              :value="priceType.value"
            >
              {{ priceType.label }}
            </option>
          </select>
        </div>

        <!-- 评分筛选 -->
        <div class="filter-group">
          <label class="filter-label">评分</label>
          <select
            v-model="selectedRating"
            @change="handleRatingChange"
            class="filter-select"
          >
            <option value="">全部评分</option>
            <option :value="4">4星以上</option>
            <option :value="3">3星以上</option>
            <option :value="2">2星以上</option>
          </select>
        </div>

        <!-- 排序 -->
        <div class="filter-group">
          <label class="filter-label">排序</label>
          <div class="flex items-center space-x-2">
            <select
              v-model="sortBy"
              @change="handleSortChange"
              class="filter-select"
            >
              <option value="name">按名称</option>
              <option value="rating">按评分</option>
              <option value="downloadCount">按下载量</option>
              <option value="lastUpdated">按更新时间</option>
              <option value="price">按价格</option>
            </select>
            <button
              @click="toggleSortOrder"
              class="p-2 rounded-lg bg-gray-100 dark:bg-dark-card hover:bg-gray-200 dark:hover:bg-dark-border transition-colors duration-200"
            >
              <component :is="sortOrder === 'asc' ? ArrowUpIcon : ArrowDownIcon" class="w-4 h-4 text-gray-600 dark:text-dark-text-secondary" />
            </button>
          </div>
        </div>

        <!-- 清除筛选 -->
        <button
          @click="clearAllFilters"
          class="px-4 py-2 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-primary-600 dark:hover:text-primary-400 bg-gray-100 dark:bg-dark-card hover:bg-gray-200 dark:hover:bg-dark-border rounded-lg transition-all duration-200"
        >
          清除筛选
        </button>
      </div>
    </div>

    <!-- 标签页 -->
    <div class="bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden">
      <el-tabs v-model="activeTab" class="app-market-tabs">
        <el-tab-pane label="全部应用" name="all">
          <AppGrid
            :apps="filteredApps"
            :loading="loading"
            @app-click="handleAppClick"
            @favorite-toggle="handleFavoriteToggle"
            @install-toggle="handleInstallToggle"
            @purchase="handlePurchase"
          />
        </el-tab-pane>
        <el-tab-pane name="favorites">
          <template #label>
            <span class="flex items-center">
              我的收藏
              <el-badge :value="favoriteCount" :hidden="favoriteCount === 0" class="ml-2" />
            </span>
          </template>
          <AppGrid
            :apps="favoriteApps"
            :loading="loading"
            @app-click="handleAppClick"
            @favorite-toggle="handleFavoriteToggle"
            @install-toggle="handleInstallToggle"
            @purchase="handlePurchase"
          />
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 应用详情弹窗 -->
    <AppDetailsDialog
      v-model="showDetailsDialog"
      :app="selectedApp"
      @favorite-toggle="handleFavoriteToggle"
      @install-toggle="handleInstallToggle"
    />

    <!-- 购买弹窗 -->
    <PurchaseDialog
      v-model="showPurchaseDialog"
      :app="selectedPurchaseApp"
      @purchase-success="handlePurchaseSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  MagnifyingGlassIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/vue/24/outline';
import AppGrid from './components/AppGrid.vue';
import AppDetailsDialog from './components/AppDetailsDialog.vue';
import PurchaseDialog from './components/PurchaseDialog.vue';
import {
  initAppMarket,
  getAllApps,
  getFavoriteApps,
  getFilteredApps,
  setFilter,
  clearFilter,
  toggleFavorite,
  toggleInstall,
  getAppById,
  appMarketStore,
  AppCategory,
  PriceType,
  type AppInfo,
  type FilterOptions
} from '../../store/app-market';

// 响应式数据
const activeTab = ref('all');
const searchKeyword = ref('');
const selectedCategory = ref('');
const selectedPriceType = ref('');
const selectedRating = ref<number | ''>('');
const sortBy = ref('name');
const sortOrder = ref<'asc' | 'desc'>('asc');
const showDetailsDialog = ref(false);
const selectedApp = ref<AppInfo | null>(null);
const showPurchaseDialog = ref(false);
const selectedPurchaseApp = ref<AppInfo | null>(null);

// 计算属性
const loading = computed(() => appMarketStore.loading.value);
const totalApps = computed(() => getAllApps().length);
const favoriteCount = computed(() => getFavoriteApps.value.length);
const installedCount = computed(() => appMarketStore.installedApps.value.length);
const filteredApps = computed(() => getFilteredApps.value);
const favoriteApps = computed(() => getFavoriteApps.value);

// 分类选项
const categoryOptions = computed(() => [
  { label: '图像处理', value: AppCategory.IMAGE_PROCESSING },
  { label: '数据分析', value: AppCategory.DATA_ANALYSIS },
  { label: 'SEO工具', value: AppCategory.SEO_TOOLS },
  { label: '市场分析', value: AppCategory.MARKET_ANALYSIS },
  { label: '管理工具', value: AppCategory.MANAGEMENT_TOOLS },
  { label: '自动化工具', value: AppCategory.AUTOMATION },
  { label: '内容创作', value: AppCategory.CONTENT_CREATION }
]);

// 价格类型选项
const priceTypeOptions = computed(() => [
  { label: '免费', value: PriceType.FREE },
  { label: '一口价', value: PriceType.ONE_TIME },
  { label: '包月', value: PriceType.MONTHLY },
  { label: '按次计费', value: PriceType.PER_USE }
]);

// 方法
const handleSearch = () => {
  updateFilter();
};

const handleCategoryChange = () => {
  updateFilter();
};

const handlePriceTypeChange = () => {
  updateFilter();
};

const handleRatingChange = () => {
  updateFilter();
};

const handleSortChange = () => {
  updateFilter();
};

const toggleSortOrder = () => {
  sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  updateFilter();
};

const updateFilter = () => {
  const filter: FilterOptions = {
    searchKeyword: searchKeyword.value || undefined,
    category: selectedCategory.value as AppCategory || undefined,
    priceType: selectedPriceType.value as PriceType || undefined,
    rating: selectedRating.value as number || undefined,
    sortBy: sortBy.value as any,
    sortOrder: sortOrder.value
  };
  setFilter(filter);
};

const clearAllFilters = () => {
  searchKeyword.value = '';
  selectedCategory.value = '';
  selectedPriceType.value = '';
  selectedRating.value = '';
  sortBy.value = 'name';
  sortOrder.value = 'asc';
  clearFilter();
};

const handleAppClick = (app: AppInfo) => {
  selectedApp.value = app;
  showDetailsDialog.value = true;
};

const handleFavoriteToggle = (appId: string) => {
  const isFavorited = toggleFavorite(appId);
  const app = getAppById(appId);
  if (app) {
    ElMessage.success(
      isFavorited ? `已收藏 ${app.name}` : `已取消收藏 ${app.name}`
    );
  }
};

const handleInstallToggle = (appId: string) => {
  const isInstalled = toggleInstall(appId);
  const app = getAppById(appId);
  if (app) {
    ElMessage.success(
      isInstalled ? `已安装 ${app.name}` : `已卸载 ${app.name}`
    );
  }
};

const handlePurchase = (app: AppInfo) => {
  selectedPurchaseApp.value = app;
  showPurchaseDialog.value = true;
};

const handlePurchaseSuccess = (appId: string) => {
  // 购买成功后自动安装应用
  handleInstallToggle(appId);
  showPurchaseDialog.value = false;
};

// 生命周期
onMounted(() => {
  initAppMarket();
});
</script>

<style scoped>
/* 应用市场标签页样式 */
:deep(.app-market-tabs) {
  .el-tabs__header {
    margin: 0;
    border-bottom: 1px solid var(--el-border-color-light);
    background: transparent;
  }

  .el-tabs__nav-wrap {
    padding: 0 24px;
  }

  .el-tabs__item {
    height: 48px;
    line-height: 48px;
    font-weight: 500;
    color: var(--el-text-color-regular);
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
  }

  .el-tabs__item:hover {
    color: var(--el-color-primary);
  }

  .el-tabs__item.is-active {
    color: var(--el-color-primary);
    border-bottom-color: var(--el-color-primary);
  }

  .el-tabs__content {
    padding: 24px;
  }
}

/* 暗黑模式适配 */
.dark :deep(.app-market-tabs) {
  .el-tabs__header {
    border-bottom-color: var(--el-border-color-dark);
  }

  .el-tabs__item {
    color: var(--el-text-color-regular);
  }

  .el-tabs__item:hover,
  .el-tabs__item.is-active {
    color: var(--el-color-primary);
  }
}

/* 筛选器样式 */
.filter-group {
  @apply flex flex-col space-y-2;
}

.filter-label {
  @apply text-sm font-medium text-gray-700 dark:text-dark-text-secondary;
}

.filter-select {
  @apply px-3 py-2 bg-gray-50 dark:bg-dark-card border border-gray-200 dark:border-dark-border rounded-lg text-sm text-gray-900 dark:text-dark-text focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200;
}

.filter-select:hover {
  @apply bg-gray-100 dark:bg-dark-border;
}

/* 优雅阴影效果 */
.shadow-elegant {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.dark .shadow-elegant-dark {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
}
</style>
