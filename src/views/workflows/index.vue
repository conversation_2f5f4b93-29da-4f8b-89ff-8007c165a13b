<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 rounded-2xl p-6 border border-primary-100 dark:border-primary-800">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
          <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text">工作流</h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary">查看工作流执行历史和管理工作流</p>
          </div>
        </div>
        <div class="flex space-x-3">
          <el-button
            @click="showCreateDialog = true"
            type="primary"
            size="large"
            :icon="Plus"
          >
            新建工作流
          </el-button>
          <!-- <el-button
            @click="showTemplateDialog = true"
            size="large"
          >
            工作流模板
          </el-button> -->
          <el-button
            @click="exportWorkflows"
            size="large"
            :icon="Download"
          >
            导出
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-dark-text-secondary">总执行次数</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-dark-text">{{ totalExecutions }}</p>
          </div>
          <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-dark-text-secondary">成功率</p>
            <p class="text-2xl font-bold text-green-600 dark:text-green-400">{{ successRate }}%</p>
          </div>
          <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-dark-text-secondary">活跃工作流</p>
            <p class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ activeWorkflows }}</p>
          </div>
          <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-dark-text-secondary">平均耗时</p>
            <p class="text-2xl font-bold text-orange-600 dark:text-orange-400">{{ averageTime }}</p>
          </div>
          <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border p-6">
      <div class="flex justify-between items-center">
        <div class="flex space-x-4">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索工作流名称..."
            style="width: 300px"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <MagnifyingGlassIcon class="w-4 h-4 text-gray-400" />
            </template>
          </el-input>

          <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px" clearable @change="handleSearch">
            <el-option label="全部" value="" />
            <el-option label="启用" value="enabled" />
            <el-option label="禁用" value="disabled" />
          </el-select>
        </div>

        <div class="flex items-center space-x-4">
          <div class="text-sm text-gray-600 dark:text-dark-text-secondary">
            共 {{ pagination.total }} 条执行记录
          </div>
          <div v-if="selectedRows.length > 0" class="flex items-center space-x-2">
            <span class="text-sm text-blue-600 dark:text-blue-400">已选择 {{ selectedRows.length }} 项</span>
            <el-button
              @click="() => ElMessage.info('批量操作功能开发中...')"
              type="primary"
              size="small"
              plain
            >
              批量启用/禁用
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 工作流执行历史列表 -->
    <div class="bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden">
      <el-table
        :data="currentPageExecutions"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        class="w-full"
        :header-cell-style="{ backgroundColor: '#f8fafc', color: '#374151', fontWeight: '600' }"
        :row-style="{ height: '80px' }"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column label="执行信息" min-width="250">
          <template #default="scope">
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 rounded-lg flex items-center justify-center"
                   :class="getExecutionIconBg(scope.row.status)">
                <component :is="getExecutionIcon(scope.row.status)" class="w-5 h-5" :class="getExecutionIconColor(scope.row.status)" />
              </div>
              <div>
                <div class="font-medium text-gray-900 dark:text-dark-text">{{ scope.row.workflowName }}</div>
                <div class="text-sm text-gray-500 dark:text-dark-text-secondary">ID: {{ scope.row.id }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="工作流程" width="280">
          <template #default="{ row }">
            <div class="py-2">
              <div class="flex items-center space-x-2 max-w-full">
                <!-- 开始节点 -->
                <div class="flex flex-col items-center space-y-1 flex-shrink-0">
                  <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                    <PlayIcon class="w-3 h-3 text-white" />
                  </div>
                  <span class="text-xs text-gray-600 dark:text-dark-text-secondary">开始</span>
                </div>

                <!-- 应用节点容器 - 支持省略号 -->
                <div class="flex items-center space-x-1 min-w-0 flex-1">
                  <!-- 显示前3个应用 -->
                  <template v-for="(app, index) in row.workflow.apps.slice(0, 3)" :key="index">
                    <!-- 连接线 -->
                    <div class="w-4 h-px bg-gray-300 dark:bg-gray-600 flex-shrink-0"></div>

                    <!-- 应用节点 -->
                    <div class="flex flex-col items-center space-y-1 flex-shrink-0">
                      <div
                        class="w-6 h-6 rounded-full flex items-center justify-center"
                        :class="getStepStatusClass(row.stepResults[index]?.status)"
                      >
                        <component :is="getAppIcon(app.type)" class="w-3 h-3 text-white" />
                      </div>
                      <span class="text-xs text-gray-600 dark:text-dark-text-secondary text-center max-w-12 truncate">
                        {{ app.name }}
                      </span>
                    </div>
                  </template>

                  <!-- 省略号（如果应用数量超过3个） -->
                  <template v-if="row.workflow.apps.length > 3">
                    <div class="w-4 h-px bg-gray-300 dark:bg-gray-600 flex-shrink-0"></div>
                    <div class="flex flex-col items-center space-y-1 flex-shrink-0">
                      <div class="w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center">
                        <span class="text-xs text-white font-bold">...</span>
                      </div>
                      <span class="text-xs text-gray-600 dark:text-dark-text-secondary">
                        +{{ row.workflow.apps.length - 3 }}
                      </span>
                    </div>
                  </template>
                </div>

                <!-- 连接线到结束 -->
                <div class="w-4 h-px bg-gray-300 dark:bg-gray-600 flex-shrink-0"></div>

                <!-- 结束节点 -->
                <div class="flex flex-col items-center space-y-1 flex-shrink-0">
                  <div class="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                    <StopIcon class="w-3 h-3 text-white" />
                  </div>
                  <span class="text-xs text-gray-600 dark:text-dark-text-secondary">结束</span>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="执行状态" width="150">
          <template #default="scope">
            <div class="space-y-2">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    :class="getStatusClass(scope.row.status)">
                <span class="w-1.5 h-1.5 rounded-full mr-1.5" :class="getStatusDotClass(scope.row.status)"></span>
                {{ getStatusText(scope.row.status) }}
              </span>
              <div v-if="scope.row.duration" class="text-xs text-gray-500 dark:text-dark-text-secondary">
                耗时: {{ scope.row.duration }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="执行信息" width="180">
          <template #default="scope">
            <div class="space-y-1">
              <div class="text-sm font-medium text-gray-900 dark:text-dark-text">{{ scope.row.executor }}</div>
              <div class="text-xs text-gray-500 dark:text-dark-text-secondary">{{ scope.row.startTime }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <div class="flex flex-col space-y-2">
              <button @click="viewExecutionDetails(scope.row)"
                class="inline-flex items-center justify-center px-3 py-1.5 text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 bg-primary-50 dark:bg-primary-900/20 hover:bg-primary-100 dark:hover:bg-primary-900/30 rounded-lg transition-all duration-200 w-full">
                查看详情
              </button>

              <!-- 更多操作下拉菜单 -->
              <el-dropdown @command="(command: string) => handleDropdownCommand(command, scope.row)" trigger="click" class="w-full">
                <button class="inline-flex items-center justify-center px-2 py-1.5 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-card hover:bg-gray-100 dark:hover:bg-dark-border rounded-lg transition-all duration-200 w-full">
                  更多操作
                  <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      :command="'viewResults'"
                      :disabled="scope.row.status !== 'completed'"
                    >
                      <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>处理结果</span>
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item command="rerun">
                      <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        <span>重新执行</span>
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item command="duplicate">
                      <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                        <span>复制工作流</span>
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item command="export">
                      <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span>导出结果</span>
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" divided>
                      <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        <span>删除记录</span>
                      </div>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="px-6 py-4 border-t border-gray-100 dark:border-dark-border">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 创建工作流弹窗 -->
    <!-- <CreateWorkflowDialog v-model="showCreateDialog" @success="refreshData" /> -->

    <!-- 工作流模板弹窗 -->
    <!-- <WorkflowTemplateDialog
      v-model="showTemplateDialog"
      @select="createFromTemplate"
      @create-blank="showCreateDialog = true"
    /> -->

    <!-- 执行详情弹窗 -->
    <!-- <ExecutionDetailsDialog
      v-model="showDetailsDialog"
      :execution="selectedExecution"
      @view-results="viewResults"
    /> -->

    <!-- 处理结果弹窗 -->
    <!-- <ResultsDialog v-model="showResultsDialog" :execution="selectedExecution" /> -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Plus, Download } from '@element-plus/icons-vue';
import {
  MagnifyingGlassIcon,
  PlayIcon,
  StopIcon
} from '@heroicons/vue/24/outline';
import {
  ShoppingBagIcon,
  ScissorsIcon,
  SparklesIcon,
  DocumentTextIcon,
  RectangleStackIcon,
  CubeIcon
} from '@heroicons/vue/24/outline';
// import CreateWorkflowDialog from './components/CreateWorkflowDialog.vue';
// import WorkflowTemplateDialog from './components/WorkflowTemplateDialog.vue';
// import ExecutionDetailsDialog from './components/ExecutionDetailsDialog.vue';
// import ResultsDialog from './components/ResultsDialog.vue';
import {
  initWorkflows,
  getWorkflowExecutions,
  // type Workflow,
  type WorkflowExecution
} from '../../store/workflows';

// 响应式数据
const searchKeyword = ref('');
const statusFilter = ref('');
const selectedRows = ref<WorkflowExecution[]>([]);
const showCreateDialog = ref(false);
// const showTemplateDialog = ref(false);
// const showDetailsDialog = ref(false);
// const showResultsDialog = ref(false);
const selectedExecution = ref<WorkflowExecution | null>(null);

const pagination = ref({
  currentPage: 1,
  pageSize: 20,
  total: 0
});

// 使用存储中的数据
const executions = ref<WorkflowExecution[]>([]);
const loading = ref(false);

// 计算属性
const filteredExecutions = computed(() => {
  let filtered = executions.value;

  // 搜索过滤
  if (searchKeyword.value) {
    filtered = filtered.filter(execution =>
      execution.workflowName.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      execution.id.toLowerCase().includes(searchKeyword.value.toLowerCase())
    );
  }

  // 状态过滤
  if (statusFilter.value) {
    filtered = filtered.filter(execution => execution.status === statusFilter.value);
  }

  return filtered;
});

const currentPageExecutions = computed(() => {
  const start = (pagination.value.currentPage - 1) * pagination.value.pageSize;
  const end = start + pagination.value.pageSize;
  return filteredExecutions.value.slice(start, end);
});

// 统计数据
const totalExecutions = computed(() => executions.value.length);

const successRate = computed(() => {
  if (executions.value.length === 0) return 0;
  const completedCount = executions.value.filter(e => e.status === 'completed').length;
  return Math.round((completedCount / executions.value.length) * 100);
});

const activeWorkflows = computed(() => {
  const uniqueWorkflows = new Set(executions.value.map(e => e.workflowId));
  return uniqueWorkflows.size;
});

const averageTime = computed(() => {
  const completedExecutions = executions.value.filter(e => e.status === 'completed' && e.duration);
  if (completedExecutions.length === 0) return '0分钟';

  // 简化计算，返回固定值作为示例
  return '3.5分钟';
});

// 获取应用图标
const getAppIcon = (appType: string) => {
  const iconMap: Record<string, any> = {
    'product-collection': ShoppingBagIcon,
    'smart-crop': ScissorsIcon,
    'one-click-cutout': SparklesIcon,
    'super-split': CubeIcon,
    'title-generator': DocumentTextIcon,
    'batch-listing': RectangleStackIcon,
    'pod-compose': CubeIcon
  };
  return iconMap[appType] || CubeIcon;
};

// 获取应用状态样式 - 已移除，使用getStepStatusClass替代

// 获取状态类型 - 已移除，使用getStatusClass替代

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'completed':
      return '已完成';
    case 'failed':
      return '失败';
    case 'running':
      return '执行中';
    case 'pending':
      return '等待中';
    default:
      return '未知';
  }
};

// 方法
const loadExecutions = async () => {
  loading.value = true;
  try {
    const data = await getWorkflowExecutions();
    executions.value = data;
    pagination.value.total = filteredExecutions.value.length;
  } catch (error) {
    ElMessage.error('加载执行历史失败');
  } finally {
    loading.value = false;
  }
};

const handleSelectionChange = (selection: WorkflowExecution[]) => {
  selectedRows.value = selection;
};

const handleSearch = () => {
  pagination.value.currentPage = 1;
  pagination.value.total = filteredExecutions.value.length;
};

const viewExecutionDetails = (execution: WorkflowExecution) => {
  selectedExecution.value = execution;
  // showDetailsDialog.value = true;
  ElMessage.info(`查看工作流执行详情: ${execution.workflowName} (ID: ${execution.id})`);
};

const viewResults = (execution: WorkflowExecution) => {
  selectedExecution.value = execution;
  // showResultsDialog.value = true;
  ElMessage.info(`查看处理结果: ${execution.workflowName}`);
};

// 获取执行图标背景
const getExecutionIconBg = (status: string) => {
  switch (status) {
    case 'completed':
      return 'bg-green-100 dark:bg-green-900/30';
    case 'failed':
      return 'bg-red-100 dark:bg-red-900/30';
    case 'running':
      return 'bg-blue-100 dark:bg-blue-900/30';
    case 'pending':
      return 'bg-gray-100 dark:bg-gray-900/30';
    default:
      return 'bg-gray-100 dark:bg-gray-900/30';
  }
};

// 获取执行图标
const getExecutionIcon = (status: string) => {
  switch (status) {
    case 'completed':
      return 'svg';
    case 'failed':
      return 'svg';
    case 'running':
      return 'svg';
    default:
      return 'svg';
  }
};

// 获取执行图标颜色
const getExecutionIconColor = (status: string) => {
  switch (status) {
    case 'completed':
      return 'text-green-600 dark:text-green-400';
    case 'failed':
      return 'text-red-600 dark:text-red-400';
    case 'running':
      return 'text-blue-600 dark:text-blue-400';
    case 'pending':
      return 'text-gray-600 dark:text-gray-400';
    default:
      return 'text-gray-600 dark:text-gray-400';
  }
};

// 获取步骤状态样式
const getStepStatusClass = (status: string) => {
  switch (status) {
    case 'completed':
      return 'bg-green-500';
    case 'failed':
      return 'bg-red-500';
    case 'running':
      return 'bg-blue-500';
    case 'pending':
      return 'bg-gray-400';
    default:
      return 'bg-gray-400';
  }
};

// 获取状态样式类
const getStatusClass = (status: string) => {
  switch (status) {
    case 'completed':
      return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
    case 'failed':
      return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
    case 'running':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
    case 'pending':
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
  }
};

// 获取状态点样式
const getStatusDotClass = (status: string) => {
  switch (status) {
    case 'completed':
      return 'bg-green-400';
    case 'failed':
      return 'bg-red-400';
    case 'running':
      return 'bg-blue-400';
    case 'pending':
      return 'bg-gray-400';
    default:
      return 'bg-gray-400';
  }
};

// 下拉菜单命令处理
const handleDropdownCommand = (command: string, row: WorkflowExecution) => {
  switch (command) {
    case 'viewResults':
      viewResults(row);
      break;
    case 'rerun':
      ElMessage.info('重新执行功能开发中...');
      break;
    case 'duplicate':
      ElMessage.info('复制工作流功能开发中...');
      break;
    case 'export':
      ElMessage.info('导出结果功能开发中...');
      break;
    case 'delete':
      ElMessage.info('删除记录功能开发中...');
      break;
  }
};

// const createFromTemplate = (template: Workflow) => {
//   // 从模板创建工作流的逻辑
//   ElMessage.success(`正在从模板"${template.name}"创建工作流...`);
//   showTemplateDialog.value = false;
//   showCreateDialog.value = true;
// };

const exportWorkflows = () => {
  ElMessage.success('导出执行历史功能开发中...');
};

// const refreshData = () => {
//   ElMessage.success('操作成功！');
//   loadExecutions();
// };

const handleSizeChange = (val: number) => {
  pagination.value.pageSize = val;
  pagination.value.currentPage = 1;
  loadExecutions();
};

const handleCurrentChange = (val: number) => {
  pagination.value.currentPage = val;
  loadExecutions();
};

// 生命周期
onMounted(() => {
  initWorkflows();
  loadExecutions();
});
</script>
