import{c,a as e,o as d,r as i,e as g,d as we,f as fe,p as ve,g as o,j as s,C as f,R as he,h as u,P as be,t as l,B as z,H as m,b as ye,Q as _e,q as M,E as y,n as _,s as R,F as B,k as Ce,y as V,S as Ee,I as Te}from"./index-ByC_LaEp.js";import{r as Se}from"./MagnifyingGlassIcon-Dye5Mj9n.js";import{r as je}from"./DocumentTextIcon-DMC-ccy0.js";function Ie(k,p){return d(),c("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"})])}function Ne(k,p){return d(),c("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m7.848 8.25 1.536.887M7.848 8.25a3 3 0 1 1-5.196-3 3 3 0 0 1 5.196 3Zm1.536.887a2.165 2.165 0 0 1 1.083 1.839c.005.351.054.695.14 1.024M9.384 9.137l2.077 1.199M7.848 15.75l1.536-.887m-1.536.887a3 3 0 1 1-5.196 3 3 3 0 0 1 5.196-3Zm1.536-.887a2.165 2.165 0 0 0 1.083-1.838c.005-.352.054-.695.14-1.025m-1.223 2.863 2.077-1.199m0-3.328a4.323 4.323 0 0 1 2.068-1.379l5.325-1.628a4.5 4.5 0 0 1 2.48-.044l.803.215-7.794 4.5m-2.882-1.664A4.33 4.33 0 0 0 10.607 12m3.736 0 7.794 4.5-.802.215a4.5 4.5 0 0 1-2.48-.043l-5.326-1.629a4.324 4.324 0 0 1-2.068-1.379M14.343 12l-2.882 1.664"})])}function ze(k,p){return d(),c("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"})])}function Me(k,p){return d(),c("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M5.25 7.5A2.25 2.25 0 0 1 7.5 5.25h9a2.25 2.25 0 0 1 2.25 2.25v9a2.25 2.25 0 0 1-2.25 2.25h-9a2.25 2.25 0 0 1-2.25-2.25v-9Z"})])}const H=i([]),W=i([]),L=i(!1),v=[{id:"WF001",name:"商品采集+智能裁图+批量刊登",apps:[{id:"app1",name:"商品采集",type:"product-collection",settings:{mode:"auto",productSelection:"manual",timeout:30,onError:"stop"}},{id:"app2",name:"智能裁图",type:"smart-crop",settings:{mode:"auto",productSelection:"previous",timeout:20,onError:"skip"}},{id:"app3",name:"批量刊登",type:"batch-listing",settings:{mode:"manual",productSelection:"previous",timeout:60,onError:"stop"}}],usageCount:156,status:"enabled",creator:"张三",createTime:"2024-01-15 10:30:00"},{id:"WF002",name:"一键抠图+超级裂变",apps:[{id:"app1",name:"一键抠图",type:"one-click-cutout",settings:{mode:"auto",productSelection:"manual",timeout:25,onError:"retry"}},{id:"app2",name:"超级裂变",type:"super-split",settings:{mode:"auto",productSelection:"previous",timeout:30,onError:"stop"}}],usageCount:89,status:"enabled",creator:"李四",createTime:"2024-01-14 14:20:00"},{id:"WF003",name:"标题生成+POD合成+批量刊登",apps:[{id:"app1",name:"标题生成",type:"title-generator",settings:{mode:"manual",productSelection:"manual",timeout:15,onError:"stop"}},{id:"app2",name:"POD合成",type:"pod-compose",settings:{mode:"auto",productSelection:"previous",timeout:45,onError:"retry"}},{id:"app3",name:"批量刊登",type:"batch-listing",settings:{mode:"manual",productSelection:"previous",timeout:60,onError:"stop"}}],usageCount:234,status:"disabled",creator:"王五",createTime:"2024-01-13 09:15:00"},{id:"WF004",name:"智能裁图+一键抠图",apps:[{id:"app1",name:"智能裁图",type:"smart-crop",settings:{mode:"auto",productSelection:"manual",timeout:20,onError:"skip"}},{id:"app2",name:"一键抠图",type:"one-click-cutout",settings:{mode:"auto",productSelection:"previous",timeout:25,onError:"retry"}}],usageCount:67,status:"enabled",creator:"赵六",createTime:"2024-01-12 16:45:00"},{id:"WF005",name:"完整电商流程",apps:[{id:"app1",name:"商品采集",type:"product-collection",settings:{mode:"auto",productSelection:"manual",timeout:30,onError:"stop"}},{id:"app2",name:"智能裁图",type:"smart-crop",settings:{mode:"auto",productSelection:"previous",timeout:20,onError:"skip"}},{id:"app3",name:"一键抠图",type:"one-click-cutout",settings:{mode:"auto",productSelection:"previous",timeout:25,onError:"retry"}},{id:"app4",name:"标题生成",type:"title-generator",settings:{mode:"manual",productSelection:"previous",timeout:15,onError:"stop"}},{id:"app5",name:"批量刊登",type:"batch-listing",settings:{mode:"manual",productSelection:"previous",timeout:60,onError:"stop"}}],usageCount:423,status:"enabled",creator:"孙七",createTime:"2024-01-11 11:20:00"}],Be=()=>{H.value=[...v]},Ve=[{id:"EXE001",workflowId:"WF001",workflowName:"商品采集+智能裁图+批量刊登",workflow:v[0],status:"completed",stepResults:[{appId:"app1",appName:"商品采集",status:"completed",startTime:"2024-01-15 10:30:00",endTime:"2024-01-15 10:35:00",duration:"5分钟",inputCount:0,outputCount:50},{appId:"app2",appName:"智能裁图",status:"completed",startTime:"2024-01-15 10:35:00",endTime:"2024-01-15 10:40:00",duration:"5分钟",inputCount:50,outputCount:50},{appId:"app3",appName:"批量刊登",status:"completed",startTime:"2024-01-15 10:40:00",endTime:"2024-01-15 10:45:00",duration:"5分钟",inputCount:50,outputCount:48}],startTime:"2024-01-15 10:30:00",endTime:"2024-01-15 10:45:00",duration:"15分钟",executor:"张三"},{id:"EXE002",workflowId:"WF002",workflowName:"一键抠图+超级裂变",workflow:v[1],status:"running",stepResults:[{appId:"app1",appName:"一键抠图",status:"completed",startTime:"2024-01-15 14:20:00",endTime:"2024-01-15 14:25:00",duration:"5分钟",inputCount:30,outputCount:30},{appId:"app2",appName:"超级裂变",status:"running",startTime:"2024-01-15 14:25:00",inputCount:30,outputCount:0}],startTime:"2024-01-15 14:20:00",duration:"8分钟",executor:"李四"},{id:"EXE003",workflowId:"WF001",workflowName:"商品采集+智能裁图+批量刊登",workflow:v[0],status:"failed",stepResults:[{appId:"app1",appName:"商品采集",status:"completed",startTime:"2024-01-15 09:00:00",endTime:"2024-01-15 09:05:00",duration:"5分钟",inputCount:0,outputCount:25},{appId:"app2",appName:"智能裁图",status:"failed",startTime:"2024-01-15 09:05:00",endTime:"2024-01-15 09:07:00",duration:"2分钟",inputCount:25,outputCount:0,errorMessage:"图片格式不支持"}],startTime:"2024-01-15 09:00:00",endTime:"2024-01-15 09:07:00",duration:"7分钟",executor:"王五"},{id:"EXE004",workflowId:"WF005",workflowName:"完整电商流程",workflow:v[4],status:"completed",stepResults:[{appId:"app1",appName:"商品采集",status:"completed",startTime:"2024-01-14 16:00:00",endTime:"2024-01-14 16:10:00",duration:"10分钟",inputCount:0,outputCount:100},{appId:"app2",appName:"智能裁图",status:"completed",startTime:"2024-01-14 16:10:00",endTime:"2024-01-14 16:20:00",duration:"10分钟",inputCount:100,outputCount:100},{appId:"app3",appName:"一键抠图",status:"completed",startTime:"2024-01-14 16:20:00",endTime:"2024-01-14 16:30:00",duration:"10分钟",inputCount:100,outputCount:95},{appId:"app4",appName:"标题生成",status:"completed",startTime:"2024-01-14 16:30:00",endTime:"2024-01-14 16:35:00",duration:"5分钟",inputCount:95,outputCount:95},{appId:"app5",appName:"批量刊登",status:"completed",startTime:"2024-01-14 16:35:00",endTime:"2024-01-14 16:45:00",duration:"10分钟",inputCount:95,outputCount:92}],startTime:"2024-01-14 16:00:00",endTime:"2024-01-14 16:45:00",duration:"45分钟",executor:"孙七"},{id:"EXE005",workflowId:"WF003",workflowName:"标题生成+POD合成+批量刊登",workflow:v[2],status:"pending",stepResults:[{appId:"app1",appName:"标题生成",status:"pending"},{appId:"app2",appName:"POD合成",status:"pending"},{appId:"app3",appName:"批量刊登",status:"pending"}],startTime:"2024-01-15 15:00:00",executor:"赵六"}],We=async()=>{L.value=!0;try{return await new Promise(k=>setTimeout(k,500)),W.value=[...Ve],W.value}finally{L.value=!1}};g(()=>H.value),g(()=>W.value),g(()=>L.value);const Le={class:"space-y-6"},Pe={class:"bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 rounded-2xl p-6 border border-primary-100 dark:border-primary-800"},De={class:"flex items-center justify-between"},Fe={class:"flex space-x-3"},$e={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Re={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},He={class:"flex items-center justify-between"},Ze={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},Ae={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Xe={class:"flex items-center justify-between"},Oe={class:"text-2xl font-bold text-green-600 dark:text-green-400"},Ue={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},qe={class:"flex items-center justify-between"},Ke={class:"text-2xl font-bold text-purple-600 dark:text-purple-400"},Qe={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Ge={class:"flex items-center justify-between"},Je={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},Ye={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border p-6"},et={class:"flex justify-between items-center"},tt={class:"flex space-x-4"},rt={class:"flex items-center space-x-4"},at={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},ot={key:0,class:"flex items-center space-x-2"},st={class:"text-sm text-blue-600 dark:text-blue-400"},nt={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},lt={class:"flex items-center space-x-3"},dt={class:"font-medium text-gray-900 dark:text-dark-text"},it={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},ut={class:"py-2"},ct={class:"flex items-center space-x-2 max-w-full"},pt={class:"flex flex-col items-center space-y-1 flex-shrink-0"},mt={class:"w-6 h-6 bg-green-500 rounded-full flex items-center justify-center"},gt={class:"flex items-center space-x-1 min-w-0 flex-1"},xt={class:"flex flex-col items-center space-y-1 flex-shrink-0"},kt={class:"text-xs text-gray-600 dark:text-dark-text-secondary text-center max-w-12 truncate"},wt={class:"flex flex-col items-center space-y-1 flex-shrink-0"},ft={class:"text-xs text-gray-600 dark:text-dark-text-secondary"},vt={class:"flex flex-col items-center space-y-1 flex-shrink-0"},ht={class:"w-6 h-6 bg-red-500 rounded-full flex items-center justify-center"},bt={class:"space-y-2"},yt={key:0,class:"text-xs text-gray-500 dark:text-dark-text-secondary"},_t={class:"space-y-1"},Ct={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Et={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},Tt={class:"flex flex-col space-y-2"},St=["onClick"],jt={class:"px-6 py-4 border-t border-gray-100 dark:border-dark-border"},Mt=we({__name:"index",setup(k){const p=i(""),C=i(""),E=i([]),Z=i(!1),P=i(null),n=i({currentPage:1,pageSize:20,total:0}),x=i([]),T=i(!1),S=g(()=>{let r=x.value;return p.value&&(r=r.filter(t=>t.workflowName.toLowerCase().includes(p.value.toLowerCase())||t.id.toLowerCase().includes(p.value.toLowerCase()))),C.value&&(r=r.filter(t=>t.status===C.value)),r}),A=g(()=>{const r=(n.value.currentPage-1)*n.value.pageSize,t=r+n.value.pageSize;return S.value.slice(r,t)}),X=g(()=>x.value.length),O=g(()=>{if(x.value.length===0)return 0;const r=x.value.filter(t=>t.status==="completed").length;return Math.round(r/x.value.length*100)}),U=g(()=>new Set(x.value.map(t=>t.workflowId)).size),q=g(()=>x.value.filter(t=>t.status==="completed"&&t.duration).length===0?"0分钟":"3.5分钟"),K=r=>({"product-collection":Te,"smart-crop":Ne,"one-click-cutout":ze,"super-split":V,"title-generator":je,"batch-listing":Ee,"pod-compose":V})[r]||V,Q=r=>{switch(r){case"completed":return"已完成";case"failed":return"失败";case"running":return"执行中";case"pending":return"等待中";default:return"未知"}},j=async()=>{T.value=!0;try{const r=await We();x.value=r,n.value.total=S.value.length}catch{m.error("加载执行历史失败")}finally{T.value=!1}},G=r=>{E.value=r},D=()=>{n.value.currentPage=1,n.value.total=S.value.length},J=r=>{P.value=r,m.info(`查看工作流执行详情: ${r.workflowName} (ID: ${r.id})`)},Y=r=>{P.value=r,m.info(`查看处理结果: ${r.workflowName}`)},ee=r=>{switch(r){case"completed":return"bg-green-100 dark:bg-green-900/30";case"failed":return"bg-red-100 dark:bg-red-900/30";case"running":return"bg-blue-100 dark:bg-blue-900/30";case"pending":return"bg-gray-100 dark:bg-gray-900/30";default:return"bg-gray-100 dark:bg-gray-900/30"}},te=r=>{switch(r){case"completed":return"svg";case"failed":return"svg";case"running":return"svg";default:return"svg"}},re=r=>{switch(r){case"completed":return"text-green-600 dark:text-green-400";case"failed":return"text-red-600 dark:text-red-400";case"running":return"text-blue-600 dark:text-blue-400";case"pending":return"text-gray-600 dark:text-gray-400";default:return"text-gray-600 dark:text-gray-400"}},ae=r=>{switch(r){case"completed":return"bg-green-500";case"failed":return"bg-red-500";case"running":return"bg-blue-500";case"pending":return"bg-gray-400";default:return"bg-gray-400"}},oe=r=>{switch(r){case"completed":return"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";case"failed":return"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";case"running":return"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400";case"pending":return"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"}},se=r=>{switch(r){case"completed":return"bg-green-400";case"failed":return"bg-red-400";case"running":return"bg-blue-400";case"pending":return"bg-gray-400";default:return"bg-gray-400"}},ne=(r,t)=>{switch(r){case"viewResults":Y(t);break;case"rerun":m.info("重新执行功能开发中...");break;case"duplicate":m.info("复制工作流功能开发中...");break;case"export":m.info("导出结果功能开发中...");break;case"delete":m.info("删除记录功能开发中...");break}},le=()=>{m.success("导出执行历史功能开发中...")},de=r=>{n.value.pageSize=r,n.value.currentPage=1,j()},ie=r=>{n.value.currentPage=r,j()};return fe(()=>{Be(),j()}),(r,t)=>{const I=u("el-button"),ue=u("el-input"),N=u("el-option"),ce=u("el-select"),w=u("el-table-column"),h=u("el-dropdown-item"),pe=u("el-dropdown-menu"),me=u("el-dropdown"),ge=u("el-table"),xe=u("el-pagination"),ke=_e("loading");return d(),c("div",Le,[e("div",Pe,[e("div",De,[t[8]||(t[8]=ve('<div class="flex items-center space-x-3"><div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center"><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg></div><div><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text">工作流</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary">查看工作流执行历史和管理工作流</p></div></div>',1)),e("div",Fe,[o(I,{onClick:t[0]||(t[0]=a=>Z.value=!0),type:"primary",size:"large",icon:f(he)},{default:s(()=>t[6]||(t[6]=[y(" 新建工作流 ")])),_:1,__:[6]},8,["icon"]),o(I,{onClick:le,size:"large",icon:f(be)},{default:s(()=>t[7]||(t[7]=[y(" 导出 ")])),_:1,__:[7]},8,["icon"])])])]),e("div",$e,[e("div",Re,[e("div",He,[e("div",null,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"总执行次数",-1)),e("p",Ze,l(X.value),1)]),t[10]||(t[10]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1))])]),e("div",Ae,[e("div",Xe,[e("div",null,[t[11]||(t[11]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"成功率",-1)),e("p",Oe,l(O.value)+"%",1)]),t[12]||(t[12]=e("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",Ue,[e("div",qe,[e("div",null,[t[13]||(t[13]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"活跃工作流",-1)),e("p",Ke,l(U.value),1)]),t[14]||(t[14]=e("div",{class:"w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1))])]),e("div",Qe,[e("div",Ge,[e("div",null,[t[15]||(t[15]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"平均耗时",-1)),e("p",Je,l(q.value),1)]),t[16]||(t[16]=e("div",{class:"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])])]),e("div",Ye,[e("div",et,[e("div",tt,[o(ue,{modelValue:p.value,"onUpdate:modelValue":t[1]||(t[1]=a=>p.value=a),placeholder:"搜索工作流名称...",style:{width:"300px"},clearable:"",onInput:D},{prefix:s(()=>[o(f(Se),{class:"w-4 h-4 text-gray-400"})]),_:1},8,["modelValue"]),o(ce,{modelValue:C.value,"onUpdate:modelValue":t[2]||(t[2]=a=>C.value=a),placeholder:"状态筛选",style:{width:"120px"},clearable:"",onChange:D},{default:s(()=>[o(N,{label:"全部",value:""}),o(N,{label:"启用",value:"enabled"}),o(N,{label:"禁用",value:"disabled"})]),_:1},8,["modelValue"])]),e("div",rt,[e("div",at," 共 "+l(n.value.total)+" 条执行记录 ",1),E.value.length>0?(d(),c("div",ot,[e("span",st,"已选择 "+l(E.value.length)+" 项",1),o(I,{onClick:t[3]||(t[3]=()=>f(m).info("批量操作功能开发中...")),type:"primary",size:"small",plain:""},{default:s(()=>t[17]||(t[17]=[y(" 批量启用/禁用 ")])),_:1,__:[17]})])):z("",!0)])])]),e("div",nt,[ye((d(),M(ge,{data:A.value,onSelectionChange:G,class:"w-full","header-cell-style":{backgroundColor:"#f8fafc",color:"#374151",fontWeight:"600"},"row-style":{height:"80px"}},{default:s(()=>[o(w,{type:"selection",width:"55"}),o(w,{label:"执行信息","min-width":"250"},{default:s(a=>[e("div",lt,[e("div",{class:_(["w-10 h-10 rounded-lg flex items-center justify-center",ee(a.row.status)])},[(d(),M(R(te(a.row.status)),{class:_(["w-5 h-5",re(a.row.status)])},null,8,["class"]))],2),e("div",null,[e("div",dt,l(a.row.workflowName),1),e("div",it,"ID: "+l(a.row.id),1)])])]),_:1}),o(w,{label:"工作流程",width:"280"},{default:s(({row:a})=>[e("div",ut,[e("div",ct,[e("div",pt,[e("div",mt,[o(f(Ie),{class:"w-3 h-3 text-white"})]),t[18]||(t[18]=e("span",{class:"text-xs text-gray-600 dark:text-dark-text-secondary"},"开始",-1))]),e("div",gt,[(d(!0),c(B,null,Ce(a.workflow.apps.slice(0,3),(b,F)=>{var $;return d(),c(B,{key:F},[t[19]||(t[19]=e("div",{class:"w-4 h-px bg-gray-300 dark:bg-gray-600 flex-shrink-0"},null,-1)),e("div",xt,[e("div",{class:_(["w-6 h-6 rounded-full flex items-center justify-center",ae(($=a.stepResults[F])==null?void 0:$.status)])},[(d(),M(R(K(b.type)),{class:"w-3 h-3 text-white"}))],2),e("span",kt,l(b.name),1)])],64)}),128)),a.workflow.apps.length>3?(d(),c(B,{key:0},[t[21]||(t[21]=e("div",{class:"w-4 h-px bg-gray-300 dark:bg-gray-600 flex-shrink-0"},null,-1)),e("div",wt,[t[20]||(t[20]=e("div",{class:"w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center"},[e("span",{class:"text-xs text-white font-bold"},"...")],-1)),e("span",ft," +"+l(a.workflow.apps.length-3),1)])],64)):z("",!0)]),t[23]||(t[23]=e("div",{class:"w-4 h-px bg-gray-300 dark:bg-gray-600 flex-shrink-0"},null,-1)),e("div",vt,[e("div",ht,[o(f(Me),{class:"w-3 h-3 text-white"})]),t[22]||(t[22]=e("span",{class:"text-xs text-gray-600 dark:text-dark-text-secondary"},"结束",-1))])])])]),_:1}),o(w,{label:"执行状态",width:"150"},{default:s(a=>[e("div",bt,[e("span",{class:_(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",oe(a.row.status)])},[e("span",{class:_(["w-1.5 h-1.5 rounded-full mr-1.5",se(a.row.status)])},null,2),y(" "+l(Q(a.row.status)),1)],2),a.row.duration?(d(),c("div",yt," 耗时: "+l(a.row.duration),1)):z("",!0)])]),_:1}),o(w,{label:"执行信息",width:"180"},{default:s(a=>[e("div",_t,[e("div",Ct,l(a.row.executor),1),e("div",Et,l(a.row.startTime),1)])]),_:1}),o(w,{label:"操作",width:"180"},{default:s(a=>[e("div",Tt,[e("button",{onClick:b=>J(a.row),class:"inline-flex items-center justify-center px-3 py-1.5 text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 bg-primary-50 dark:bg-primary-900/20 hover:bg-primary-100 dark:hover:bg-primary-900/30 rounded-lg transition-all duration-200 w-full"}," 查看详情 ",8,St),o(me,{onCommand:b=>ne(b,a.row),trigger:"click",class:"w-full"},{dropdown:s(()=>[o(pe,null,{default:s(()=>[o(h,{command:"viewResults",disabled:a.row.status!=="completed"},{default:s(()=>t[24]||(t[24]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})]),e("span",null,"处理结果")],-1)])),_:2,__:[24]},1032,["disabled"]),o(h,{command:"rerun"},{default:s(()=>t[25]||(t[25]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})]),e("span",null,"重新执行")],-1)])),_:1,__:[25]}),o(h,{command:"duplicate"},{default:s(()=>t[26]||(t[26]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-purple-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})]),e("span",null,"复制工作流")],-1)])),_:1,__:[26]}),o(h,{command:"export"},{default:s(()=>t[27]||(t[27]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})]),e("span",null,"导出结果")],-1)])),_:1,__:[27]}),o(h,{command:"delete",divided:""},{default:s(()=>t[28]||(t[28]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})]),e("span",null,"删除记录")],-1)])),_:1,__:[28]})]),_:2},1024)]),default:s(()=>[t[29]||(t[29]=e("button",{class:"inline-flex items-center justify-center px-2 py-1.5 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-card hover:bg-gray-100 dark:hover:bg-dark-border rounded-lg transition-all duration-200 w-full"},[y(" 更多操作 "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))]),_:2,__:[29]},1032,["onCommand"])])]),_:1})]),_:1},8,["data"])),[[ke,T.value]]),e("div",jt,[o(xe,{"current-page":n.value.currentPage,"onUpdate:currentPage":t[4]||(t[4]=a=>n.value.currentPage=a),"page-size":n.value.pageSize,"onUpdate:pageSize":t[5]||(t[5]=a=>n.value.pageSize=a),total:n.value.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:de,onCurrentChange:ie},null,8,["current-page","page-size","total"])])])])}}});export{Mt as default};
