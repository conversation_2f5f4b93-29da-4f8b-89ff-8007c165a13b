import{c as a,o as s,a as e,d as N,r as p,e as ee,f as G,g as n,h as I,j as _,F as C,k as M,t as o,n as j,l as F,m as te,p as ge,q as B,s as A,x as H,y as R,z as re,A as xe,B as X,C as T,D as O,E as L,G as Q,H as g,I as se,J as ae,b as me,K as pe,L as ke}from"./index-ByC_LaEp.js";import{r as J}from"./DocumentTextIcon-DMC-ccy0.js";import{r as Y}from"./InformationCircleIcon-DqPQfm_b.js";import{r as K}from"./CheckCircleIcon-DQ6MhbFo.js";import{r as oe}from"./ClockIcon-BU00D9Lq.js";import{r as ne}from"./ClipboardDocumentListIcon-BVsKGqSE.js";import{r as be}from"./ArrowDownTrayIcon-D0qky6zr.js";import{r as de}from"./ChartBarIcon-BcAYokyM.js";import{r as ve}from"./ShieldCheckIcon-Bm28tbog.js";import{r as ye}from"./CreditCardIcon-Ch2YxyAA.js";function he(w,u){return s(),a("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5"})])}function fe(w,u){return s(),a("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"})])}function _e(w,u){return s(),a("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"})])}function we(w,u){return s(),a("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m19.5 8.25-7.5 7.5-7.5-7.5"})])}function E(w,u){return s(),a("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"})])}function $e(w,u){return s(),a("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m4.5 15.75 7.5-7.5 7.5 7.5"})])}function Ce(w,u){return s(),a("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125"})])}function Me(w,u){return s(),a("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 15a4.5 4.5 0 0 0 4.5 4.5H18a3.75 3.75 0 0 0 1.332-7.257 3 3 0 0 0-3.758-3.848 5.25 5.25 0 0 0-10.233 2.33A4.502 4.502 0 0 0 2.25 15Z"})])}function je(w,u){return s(),a("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M10.343 3.94c.09-.542.56-.94 1.11-.94h1.093c.55 0 1.02.398 1.11.94l.149.894c.07.424.384.764.78.93.398.164.855.142 1.205-.108l.737-.527a1.125 1.125 0 0 1 1.45.12l.773.774c.39.389.44 1.002.12 1.45l-.527.737c-.25.35-.272.806-.107 1.204.165.397.505.71.93.78l.893.15c.543.09.94.559.94 1.109v1.094c0 .55-.397 1.02-.94 1.11l-.894.149c-.424.07-.764.383-.929.78-.165.398-.143.854.107 1.204l.527.738c.32.447.269 1.06-.12 1.45l-.774.773a1.125 1.125 0 0 1-1.449.12l-.738-.527c-.35-.25-.806-.272-1.203-.107-.398.165-.71.505-.781.929l-.149.894c-.09.542-.56.94-1.11.94h-1.094c-.55 0-1.019-.398-1.11-.94l-.148-.894c-.071-.424-.384-.764-.781-.93-.398-.164-.854-.142-1.204.108l-.738.527c-.447.32-1.06.269-1.45-.12l-.773-.774a1.125 1.125 0 0 1-.12-1.45l.527-.737c.25-.35.272-.806.108-1.204-.165-.397-.506-.71-.93-.78l-.894-.15c-.542-.09-.94-.56-.94-1.109v-1.094c0-.55.398-1.02.94-1.11l.894-.149c.424-.07.765-.383.93-.78.165-.398.143-.854-.108-1.204l-.526-.738a1.125 1.125 0 0 1 .12-1.45l.773-.773a1.125 1.125 0 0 1 1.45-.12l.737.527c.35.25.807.272 1.204.107.397-.165.71-.505.78-.929l.15-.894Z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"})])}function Te(w,u){return s(),a("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z"})])}function Ve(w,u){return s(),a("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 12.75V12A2.25 2.25 0 0 1 4.5 9.75h15A2.25 2.25 0 0 1 21.75 12v.75m-8.69-6.44-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z"})])}function Be(w,u){return s(),a("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"})])}function Ae(w,u){return s(),a("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])}const Ue={class:"bg-white dark:bg-dark-card rounded-xl p-6 shadow-sm border border-gray-100 dark:border-dark-border"},Se={class:"flex items-center justify-between mb-6"},De={class:"flex items-center space-x-2"},Ie={class:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8"},ze={class:"space-y-3"},Pe={class:"flex items-center space-x-3"},Le={class:"font-medium text-gray-900 dark:text-dark-text"},qe={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Fe={class:"text-right"},He={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Re={class:"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1"},Ne={class:"relative"},Ze={class:"w-32 h-32 mx-auto mb-4"},Ee={viewBox:"0 0 100 100",class:"w-full h-full transform -rotate-90"},Ge=["stroke-dashoffset"],Oe=["stroke-dashoffset"],Qe={class:"absolute inset-0 flex items-center justify-center"},Je={class:"text-center"},Ke={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},We={class:"space-y-2"},Xe={class:"flex items-center justify-between"},Ye={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},et={class:"flex items-center justify-between"},tt={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},rt={class:"flex items-center justify-between"},st={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},at={class:"h-64 bg-gray-50 dark:bg-dark-border rounded-lg p-4"},ot={class:"h-full flex items-end justify-between space-x-2"},nt=["title"],dt={class:"flex justify-between mt-2 text-xs text-gray-500 dark:text-dark-text-secondary"},lt={class:"mt-8"},it={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},ct={class:"flex items-center justify-between mb-2"},ut={class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary"},gt={class:"text-lg font-bold text-gray-900 dark:text-dark-text"},xt={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},mt={class:"flex justify-between mt-1 text-xs text-gray-500 dark:text-dark-text-secondary"},pt=N({__name:"DataDashboard",setup(w){const u=p("today"),f=p([{id:"product-collection",name:"商品采集",usage:156,percentage:35},{id:"smart-crop",name:"智能裁图",usage:128,percentage:28},{id:"one-click-cutout",name:"一键抠图",usage:89,percentage:20},{id:"title-generator",name:"标题生成",usage:67,percentage:15},{id:"batch-listing",name:"批量刊登",usage:23,percentage:5}]),k=p({completed:75,processing:18,failed:7}),b=p([{label:"周一",value:120},{label:"周二",value:156},{label:"周三",value:189},{label:"周四",value:167},{label:"周五",value:234},{label:"周六",value:198},{label:"周日",value:145}]),x=p([{id:"success-rate",label:"成功率",value:"94.2%",percentage:94,status:"excellent"},{id:"avg-time",label:"平均处理时间",value:"2.3s",percentage:85,status:"good"},{id:"resource-usage",label:"资源使用率",value:"67%",percentage:67,status:"warning"}]),U=ee(()=>f.value.reduce(($,i)=>$+i.usage,0)),V=$=>["bg-gradient-to-br from-yellow-400 to-yellow-500","bg-gradient-to-br from-gray-400 to-gray-500","bg-gradient-to-br from-amber-600 to-amber-700","bg-gradient-to-br from-blue-500 to-blue-600","bg-gradient-to-br from-purple-500 to-purple-600"][$]||"bg-gradient-to-br from-gray-500 to-gray-600",S=$=>({excellent:"bg-gradient-to-r from-green-500 to-green-600",good:"bg-gradient-to-r from-blue-500 to-blue-600",warning:"bg-gradient-to-r from-amber-500 to-amber-600",poor:"bg-gradient-to-r from-red-500 to-red-600"})[$]||"bg-gray-500";return G(()=>{}),($,i)=>{const v=I("el-option"),y=I("el-select");return s(),a("div",Ue,[e("div",Se,[i[1]||(i[1]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"数据看板",-1)),e("div",De,[n(y,{modelValue:u.value,"onUpdate:modelValue":i[0]||(i[0]=l=>u.value=l),size:"small",class:"w-24"},{default:_(()=>[n(v,{label:"今日",value:"today"}),n(v,{label:"本周",value:"week"}),n(v,{label:"本月",value:"month"})]),_:1},8,["modelValue"])])]),e("div",Ie,[e("div",null,[i[2]||(i[2]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-4"},"应用使用排行",-1)),e("div",ze,[(s(!0),a(C,null,M(f.value,(l,c)=>(s(),a("div",{key:l.id,class:"flex items-center justify-between p-3 bg-gray-50 dark:bg-dark-border rounded-lg"},[e("div",Pe,[e("div",{class:j(["w-8 h-8 rounded-lg flex items-center justify-center text-sm font-bold text-white",V(c)])},o(c+1),3),e("div",null,[e("div",Le,o(l.name),1),e("div",qe,o(l.usage)+"次使用",1)])]),e("div",Fe,[e("div",He,o(l.percentage)+"%",1),e("div",Re,[e("div",{class:"bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-300",style:F({width:`${l.percentage}%`})},null,4)])])]))),128))])]),e("div",null,[i[8]||(i[8]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-4"},"任务状态分布",-1)),e("div",Ne,[e("div",Ze,[(s(),a("svg",Ee,[i[3]||(i[3]=e("circle",{cx:"50",cy:"50",r:"40",fill:"none",stroke:"#e5e7eb","stroke-width":"8",class:"dark:stroke-gray-700"},null,-1)),e("circle",{cx:"50",cy:"50",r:"40",fill:"none",stroke:"#10b981","stroke-width":"8","stroke-dasharray":"188.5","stroke-dashoffset":188.5-188.5*k.value.completed/100,class:"transition-all duration-500"},null,8,Ge),e("circle",{cx:"50",cy:"50",r:"40",fill:"none",stroke:"#f59e0b","stroke-width":"8","stroke-dasharray":"188.5","stroke-dashoffset":188.5-188.5*(k.value.completed+k.value.processing)/100,class:"transition-all duration-500"},null,8,Oe)])),e("div",Qe,[e("div",Je,[e("div",Ke,o(U.value),1),i[4]||(i[4]=e("div",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"总任务",-1))])])]),e("div",We,[e("div",Xe,[i[5]||(i[5]=e("div",{class:"flex items-center space-x-2"},[e("div",{class:"w-3 h-3 bg-green-500 rounded-full"}),e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"已完成")],-1)),e("span",Ye,o(k.value.completed)+"% ",1)]),e("div",et,[i[6]||(i[6]=e("div",{class:"flex items-center space-x-2"},[e("div",{class:"w-3 h-3 bg-amber-500 rounded-full"}),e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"处理中")],-1)),e("span",tt,o(k.value.processing)+"% ",1)]),e("div",rt,[i[7]||(i[7]=e("div",{class:"flex items-center space-x-2"},[e("div",{class:"w-3 h-3 bg-red-500 rounded-full"}),e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"失败")],-1)),e("span",st,o(k.value.failed)+"% ",1)])])])])]),e("div",null,[i[9]||(i[9]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-4"},"使用趋势",-1)),e("div",at,[e("div",ot,[(s(!0),a(C,null,M(b.value,(l,c)=>(s(),a("div",{key:c,class:"flex-1 bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-sm transition-all duration-300 hover:from-blue-600 hover:to-blue-500",style:F({height:`${l.value/Math.max(...b.value.map(r=>r.value))*100}%`}),title:`${l.label}: ${l.value}`},null,12,nt))),128))]),e("div",dt,[(s(!0),a(C,null,M(b.value,l=>(s(),a("span",{key:l.label},o(l.label),1))),128))])])]),e("div",lt,[i[10]||(i[10]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-4"},"性能指标",-1)),e("div",it,[(s(!0),a(C,null,M(x.value,l=>(s(),a("div",{key:l.id,class:"p-4 bg-gray-50 dark:bg-dark-border rounded-lg"},[e("div",ct,[e("span",ut,o(l.label),1),e("span",gt,o(l.value),1)]),e("div",xt,[e("div",{class:j(["h-2 rounded-full transition-all duration-300",S(l.status)]),style:F({width:`${l.percentage}%`})},null,6)]),e("div",mt,[e("span",null,o(l.status),1),e("span",null,o(l.percentage)+"%",1)])]))),128))])])])}}}),kt={class:"bg-white dark:bg-dark-card rounded-xl p-6 shadow-sm border border-gray-100 dark:border-dark-border"},bt={class:"mb-8"},vt={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},yt={class:"flex items-center justify-between mb-2"},ht={class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary"},ft={class:"text-lg font-bold text-gray-900 dark:text-dark-text"},_t={class:"text-xs text-gray-500 dark:text-dark-text-secondary mt-1"},wt={class:"mb-8"},$t={class:"flex items-center justify-between mb-4"},Ct={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Mt={class:"space-y-3 max-h-64 overflow-y-auto"},jt={class:"flex items-center justify-between mb-2"},Tt={class:"flex items-center space-x-2"},Vt={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Bt={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},At={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2"},Ut={class:"flex justify-between text-xs text-gray-500 dark:text-dark-text-secondary"},St={class:"mb-8"},Dt={class:"space-y-4"},It={class:"flex items-center space-x-3"},zt={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Pt={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},Lt={class:"text-right"},qt={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Ft={class:"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1"},Ht={class:"space-y-3 max-h-48 overflow-y-auto"},Rt={class:"flex-1 min-w-0"},Nt={class:"text-sm text-gray-900 dark:text-dark-text"},Zt={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},Et=N({__name:"RealTimeMonitoring",setup(w){const u=p([{id:"api",name:"API服务",value:"正常",status:"healthy",description:"响应时间: 120ms"},{id:"database",name:"数据库",value:"正常",status:"healthy",description:"连接数: 45/100"},{id:"storage",name:"存储服务",value:"警告",status:"warning",description:"使用率: 85%"}]),f=p([{id:"task1",name:"商品采集 - Amazon平台",type:"product-collection",progress:75,operator:"张三",estimatedTime:"5分钟"},{id:"task2",name:"智能裁图批处理",type:"smart-crop",progress:45,operator:"李四",estimatedTime:"12分钟"},{id:"task3",name:"标题生成任务",type:"title-generator",progress:90,operator:"王五",estimatedTime:"2分钟"}]),k=p([{id:"cpu",name:"CPU使用率",description:"处理器负载",usage:65,icon:Te},{id:"memory",name:"内存使用率",description:"8GB / 16GB",usage:50,icon:Ce},{id:"storage",name:"存储使用率",description:"850GB / 1TB",usage:85,icon:Me}]),b=p([{id:"act1",type:"success",message:"商品采集任务 #12345 已完成",time:"2分钟前"},{id:"act2",type:"info",message:"用户 张三 登录系统",time:"5分钟前"},{id:"act3",type:"warning",message:"存储空间使用率超过80%",time:"10分钟前"},{id:"act4",type:"success",message:"智能裁图任务 #12344 已完成",time:"15分钟前"},{id:"act5",type:"error",message:"批量刊登任务 #12343 执行失败",time:"20分钟前"}]),x=l=>({healthy:"bg-green-500",warning:"bg-amber-500",error:"bg-red-500"})[l]||"bg-gray-500",U=l=>({healthy:"border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/10",warning:"border-amber-200 dark:border-amber-800 bg-amber-50 dark:bg-amber-900/10",error:"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/10"})[l]||"border-gray-200 dark:border-gray-700",V=l=>({"product-collection":R,"smart-crop":H,"title-generator":J})[l]||R,S=l=>l>=80?"bg-gradient-to-r from-red-500 to-red-600":l>=60?"bg-gradient-to-r from-amber-500 to-amber-600":"bg-gradient-to-r from-green-500 to-green-600",$=l=>({success:"bg-green-500",warning:"bg-amber-500",error:"bg-red-500",info:"bg-blue-500"})[l]||"bg-gray-500",i=l=>({success:K,warning:re,error:Ae,info:Y})[l]||Y,v=()=>{f.value.forEach(l=>{l.progress<100&&(l.progress=Math.min(100,l.progress+Math.random()*5))}),k.value.forEach(l=>{const c=(Math.random()-.5)*10;l.usage=Math.max(0,Math.min(100,l.usage+c))})};let y;return G(()=>{y=setInterval(v,3e3)}),te(()=>{y&&clearInterval(y)}),(l,c)=>(s(),a("div",kt,[c[4]||(c[4]=ge('<div class="flex items-center justify-between mb-6"><h3 class="text-lg font-semibold text-gray-900 dark:text-dark-text">实时监控</h3><div class="flex items-center space-x-2"><div class="flex items-center space-x-1"><div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div><span class="text-sm text-gray-600 dark:text-dark-text-secondary">实时更新</span></div></div></div>',1)),e("div",bt,[c[0]||(c[0]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-4"},"系统状态",-1)),e("div",vt,[(s(!0),a(C,null,M(u.value,r=>(s(),a("div",{key:r.id,class:j(["p-4 rounded-lg border",U(r.status)])},[e("div",yt,[e("span",ht,o(r.name),1),e("div",{class:j(["w-3 h-3 rounded-full",x(r.status)])},null,2)]),e("div",ft,o(r.value),1),e("div",_t,o(r.description),1)],2))),128))])]),e("div",wt,[e("div",$t,[c[1]||(c[1]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary"},"正在处理的任务",-1)),e("span",Ct,o(f.value.length)+" 个任务",1)]),e("div",Mt,[(s(!0),a(C,null,M(f.value,r=>(s(),a("div",{key:r.id,class:"p-3 bg-gray-50 dark:bg-dark-border rounded-lg"},[e("div",jt,[e("div",Tt,[(s(),B(A(V(r.type)),{class:"w-4 h-4 text-gray-600 dark:text-dark-text-secondary"})),e("span",Vt,o(r.name),1)]),e("span",Bt,o(r.progress)+"%",1)]),e("div",At,[e("div",{class:"bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-300",style:F({width:`${r.progress}%`})},null,4)]),e("div",Ut,[e("span",null,o(r.operator),1),e("span",null,"预计剩余: "+o(r.estimatedTime),1)])]))),128))])]),e("div",St,[c[2]||(c[2]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-4"},"资源使用情况",-1)),e("div",Dt,[(s(!0),a(C,null,M(k.value,r=>(s(),a("div",{key:r.id,class:"flex items-center justify-between"},[e("div",It,[(s(),B(A(r.icon),{class:"w-5 h-5 text-gray-600 dark:text-dark-text-secondary"})),e("div",null,[e("div",zt,o(r.name),1),e("div",Pt,o(r.description),1)])]),e("div",Lt,[e("div",qt,o(r.usage)+"%",1),e("div",Ft,[e("div",{class:j(["h-2 rounded-full transition-all duration-300",S(r.usage)]),style:F({width:`${r.usage}%`})},null,6)])])]))),128))])]),e("div",null,[c[3]||(c[3]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-4"},"最近活动",-1)),e("div",Ht,[(s(!0),a(C,null,M(b.value,r=>(s(),a("div",{key:r.id,class:"flex items-start space-x-3 p-2 hover:bg-gray-50 dark:hover:bg-dark-border rounded-lg transition-colors"},[e("div",{class:j(["w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0",$(r.type)])},[(s(),B(A(i(r.type)),{class:"w-4 h-4 text-white"}))],2),e("div",Rt,[e("div",Nt,o(r.message),1),e("div",Zt,o(r.time),1)])]))),128))])])]))}}),Gt={class:"bg-white dark:bg-dark-card rounded-xl p-6 shadow-sm border border-gray-100 dark:border-dark-border"},Ot={class:"flex items-center justify-between mb-6"},Qt={class:"flex items-center space-x-2"},Jt={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-400"},Kt={key:0,class:"text-center py-8"},Wt={key:1,class:"space-y-4"},Xt={class:"flex items-start justify-between mb-2"},Yt={class:"flex items-center space-x-2"},er={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},tr={class:"flex items-center space-x-1"},rr=["onClick"],sr=["onClick"],ar={class:"text-sm text-gray-600 dark:text-dark-text-secondary mb-3"},or={class:"flex items-center justify-between text-xs text-gray-500 dark:text-dark-text-secondary"},nr={class:"flex items-center space-x-4"},dr={class:"flex items-center space-x-2"},lr={key:0,class:"mt-3"},ir={class:"flex justify-between text-xs text-gray-500 dark:text-dark-text-secondary mb-1"},cr={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5"},ur={class:"mt-6 pt-4 border-t border-gray-200 dark:border-dark-border"},gr={class:"flex items-center justify-between"},xr={class:"flex space-x-2"},mr={class:"flex justify-end space-x-3"},pr=N({__name:"UrgentTasks",setup(w){const u=p(!1),f=p(!1),k=p(),b=p([{id:"urgent-1",title:"存储空间不足警告",description:"系统存储空间使用率已达到85%，需要立即清理或扩容",priority:"urgent",type:"系统故障",assignee:"张三",deadline:"2小时内",progress:30,status:"in-progress"},{id:"urgent-2",title:"批量任务处理失败",description:"商品采集任务 #12340 处理失败，影响50个商品数据",priority:"high",type:"数据处理",assignee:"李四",deadline:"4小时内",status:"pending"},{id:"urgent-3",title:"客户反馈处理延迟",description:"有3个客户反馈处理超时，需要立即跟进",priority:"medium",type:"客户支持",assignee:"王五",deadline:"今日内",progress:60,status:"in-progress"}]),x=xe({title:"",priority:"",type:"",deadline:null,description:"",assignee:""}),U={title:[{required:!0,message:"请输入任务标题",trigger:"blur"}],priority:[{required:!0,message:"请选择优先级",trigger:"change"}],type:[{required:!0,message:"请选择任务类型",trigger:"change"}],deadline:[{required:!0,message:"请选择截止时间",trigger:"change"}],description:[{required:!0,message:"请输入任务描述",trigger:"blur"}],assignee:[{required:!0,message:"请选择负责人",trigger:"change"}]},V=h=>({urgent:"bg-red-500",high:"bg-orange-500",medium:"bg-yellow-500"})[h]||"bg-gray-500",S=h=>({urgent:"border-red-500 bg-red-50 dark:bg-red-900/10",high:"border-orange-500 bg-orange-50 dark:bg-orange-900/10",medium:"border-yellow-500 bg-yellow-50 dark:bg-yellow-900/10"})[h]||"border-gray-500",$=h=>({urgent:"bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-400",high:"bg-orange-100 dark:bg-orange-900/20 text-orange-800 dark:text-orange-400",medium:"bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-400"})[h]||"bg-gray-100 text-gray-800",i=h=>({urgent:"紧急",high:"高",medium:"中"})[h]||h,v=h=>h>=80?"bg-green-500":h>=50?"bg-blue-500":h>=20?"bg-yellow-500":"bg-red-500",y=h=>{g.info(`开始处理任务: ${h.title}`)},l=h=>{const t=b.value.findIndex(D=>D.id===h.id);t>-1&&(b.value.splice(t,1),g.success("任务已忽略"))},c=()=>{g.success("紧急任务已刷新")},r=()=>{u.value=!0},d=()=>{g.info("跳转到任务管理页面")},P=async()=>{var h;if(k.value)try{await k.value.validate(),f.value=!0,await new Promise(D=>setTimeout(D,1e3));const t={id:`urgent-${Date.now()}`,title:x.title,description:x.description,priority:x.priority,type:x.type,assignee:x.assignee,deadline:((h=x.deadline)==null?void 0:h.toLocaleString())||"",status:"pending"};b.value.unshift(t),k.value.resetFields(),u.value=!1,g.success("紧急任务创建成功")}catch{g.error("创建失败，请检查输入信息")}finally{f.value=!1}};return(h,t)=>{const D=I("el-input"),q=I("el-form-item"),z=I("el-option"),Z=I("el-select"),le=I("el-date-picker"),ie=I("el-form"),W=I("el-button"),ce=I("el-dialog");return s(),a("div",Gt,[e("div",Ot,[e("div",Qt,[n(T(re),{class:"w-5 h-5 text-red-500"}),t[8]||(t[8]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"紧急任务",-1)),b.value.length>0?(s(),a("span",Jt,o(b.value.length),1)):X("",!0)]),e("button",{onClick:c,class:"text-sm text-gray-500 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text"},[n(T(O),{class:"w-4 h-4"})])]),b.value.length===0?(s(),a("div",Kt,[n(T(K),{class:"w-12 h-12 text-green-500 mx-auto mb-3"}),t[9]||(t[9]=e("p",{class:"text-gray-500 dark:text-dark-text-secondary"},"暂无紧急任务",-1))])):(s(),a("div",Wt,[(s(!0),a(C,null,M(b.value,m=>(s(),a("div",{key:m.id,class:j(["p-4 rounded-lg border-l-4 transition-all duration-200 hover:shadow-md",S(m.priority)])},[e("div",Xt,[e("div",Yt,[e("div",{class:j(["w-2 h-2 rounded-full",V(m.priority)])},null,2),e("span",er,o(m.title),1),e("span",{class:j(["inline-flex items-center px-2 py-0.5 rounded text-xs font-medium",$(m.priority)])},o(i(m.priority)),3)]),e("div",tr,[e("button",{onClick:ue=>y(m),class:"text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"}," 处理 ",8,rr),e("button",{onClick:ue=>l(m),class:"text-xs text-gray-500 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text"}," 忽略 ",8,sr)])]),e("p",ar,o(m.description),1),e("div",or,[e("div",nr,[e("span",null,o(m.type),1),e("span",null,o(m.assignee),1)]),e("div",dr,[n(T(oe),{class:"w-3 h-3"}),e("span",null,o(m.deadline),1)])]),m.progress!==void 0?(s(),a("div",lr,[e("div",ir,[t[10]||(t[10]=e("span",null,"进度",-1)),e("span",null,o(m.progress)+"%",1)]),e("div",cr,[e("div",{class:j(["h-1.5 rounded-full transition-all duration-300",v(m.progress)]),style:F({width:`${m.progress}%`})},null,6)])])):X("",!0)],2))),128))])),e("div",ur,[e("div",gr,[t[12]||(t[12]=e("span",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary"},"快速操作",-1)),e("div",xr,[e("button",{onClick:r,class:"inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors"},[n(T(Q),{class:"w-3 h-3 mr-1"}),t[11]||(t[11]=L(" 新建紧急任务 "))]),e("button",{onClick:d,class:"inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-900 dark:hover:text-dark-text bg-gray-100 dark:bg-dark-border hover:bg-gray-200 dark:hover:bg-dark-card rounded-lg transition-colors"}," 查看全部 ")])])]),n(ce,{modelValue:u.value,"onUpdate:modelValue":t[7]||(t[7]=m=>u.value=m),title:"创建紧急任务",width:"500px"},{footer:_(()=>[e("div",mr,[n(W,{onClick:t[6]||(t[6]=m=>u.value=!1)},{default:_(()=>t[13]||(t[13]=[L("取消")])),_:1,__:[13]}),n(W,{type:"primary",onClick:P,loading:f.value},{default:_(()=>t[14]||(t[14]=[L(" 创建任务 ")])),_:1,__:[14]},8,["loading"])])]),default:_(()=>[n(ie,{ref_key:"createFormRef",ref:k,model:x,rules:U,"label-width":"80px"},{default:_(()=>[n(q,{label:"任务标题",prop:"title"},{default:_(()=>[n(D,{modelValue:x.title,"onUpdate:modelValue":t[0]||(t[0]=m=>x.title=m),placeholder:"请输入任务标题"},null,8,["modelValue"])]),_:1}),n(q,{label:"优先级",prop:"priority"},{default:_(()=>[n(Z,{modelValue:x.priority,"onUpdate:modelValue":t[1]||(t[1]=m=>x.priority=m),placeholder:"选择优先级",class:"w-full"},{default:_(()=>[n(z,{label:"紧急",value:"urgent"}),n(z,{label:"高",value:"high"}),n(z,{label:"中",value:"medium"})]),_:1},8,["modelValue"])]),_:1}),n(q,{label:"任务类型",prop:"type"},{default:_(()=>[n(Z,{modelValue:x.type,"onUpdate:modelValue":t[2]||(t[2]=m=>x.type=m),placeholder:"选择任务类型",class:"w-full"},{default:_(()=>[n(z,{label:"系统故障",value:"system-error"}),n(z,{label:"数据处理",value:"data-processing"}),n(z,{label:"客户支持",value:"customer-support"}),n(z,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1}),n(q,{label:"截止时间",prop:"deadline"},{default:_(()=>[n(le,{modelValue:x.deadline,"onUpdate:modelValue":t[3]||(t[3]=m=>x.deadline=m),type:"datetime",placeholder:"选择截止时间",class:"w-full"},null,8,["modelValue"])]),_:1}),n(q,{label:"任务描述",prop:"description"},{default:_(()=>[n(D,{modelValue:x.description,"onUpdate:modelValue":t[4]||(t[4]=m=>x.description=m),type:"textarea",rows:3,placeholder:"请输入任务描述"},null,8,["modelValue"])]),_:1}),n(q,{label:"指派给",prop:"assignee"},{default:_(()=>[n(Z,{modelValue:x.assignee,"onUpdate:modelValue":t[5]||(t[5]=m=>x.assignee=m),placeholder:"选择负责人",class:"w-full"},{default:_(()=>[n(z,{label:"张三",value:"张三"}),n(z,{label:"李四",value:"李四"}),n(z,{label:"王五",value:"王五"})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),kr={class:"bg-white dark:bg-dark-card rounded-xl p-6 shadow-sm border border-gray-100 dark:border-dark-border"},br={class:"flex items-center justify-between mb-6"},vr={class:"mb-6"},yr={class:"flex items-center justify-between mb-3"},hr={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},fr={key:0,class:"text-center py-4"},_r={key:1,class:"space-y-2 max-h-48 overflow-y-auto"},wr=["onClick"],$r={class:"flex items-center space-x-3"},Cr={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Mr={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},jr={class:"mb-6"},Tr={class:"space-y-2"},Vr={class:"flex items-center space-x-3"},Br={class:"text-sm text-gray-900 dark:text-dark-text"},Ar={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},Ur={class:"flex items-center space-x-2"},Sr=["onClick"],Dr={class:"border-t border-gray-200 dark:border-dark-border pt-4"},Ir={class:"grid grid-cols-2 gap-2"},zr=["onClick"],Pr={class:"mt-6 pt-4 border-t border-gray-200 dark:border-dark-border"},Lr={class:"grid grid-cols-3 gap-4 text-center"},qr={class:"text-lg font-bold text-gray-900 dark:text-dark-text"},Fr={class:"text-lg font-bold text-gray-900 dark:text-dark-text"},Hr={class:"text-lg font-bold text-gray-900 dark:text-dark-text"},Rr=N({__name:"CommonTasks",setup(w){const u=p([{id:"pending-1",title:"审核商品采集结果",type:"商品采集",status:"urgent",createTime:"30分钟前"},{id:"pending-2",title:"处理图片裁剪失败",type:"智能裁图",status:"normal",createTime:"1小时前"},{id:"pending-3",title:"确认批量刊登设置",type:"批量刊登",status:"normal",createTime:"2小时前"}]),f=p([{id:"recent-1",title:"Amazon商品采集 #12345",type:"product-collection",status:"completed",finishTime:"5分钟前"},{id:"recent-2",title:"智能裁图批处理 #12344",type:"smart-crop",status:"completed",finishTime:"15分钟前"},{id:"recent-3",title:"标题生成任务 #12343",type:"title-generator",status:"failed",finishTime:"25分钟前"},{id:"recent-4",title:"一键抠图任务 #12342",type:"one-click-cutout",status:"completed",finishTime:"35分钟前"}]),k=p([{id:"new-collection",name:"新建采集",icon:Q,action:"create-collection"},{id:"batch-process",name:"批量处理",icon:ne,action:"batch-process"},{id:"view-gallery",name:"查看图库",icon:H,action:"view-gallery"},{id:"system-settings",name:"系统设置",icon:ae,action:"system-settings"}]),b=p({completed:156,processing:23,pending:8}),x=c=>({urgent:"bg-red-500",normal:"bg-blue-500",pending:"bg-gray-500"})[c]||"bg-gray-500",U=c=>({"product-collection":R,"smart-crop":H,"title-generator":J,"one-click-cutout":H,"batch-listing":se})[c]||R,V=c=>({completed:"bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400",failed:"bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-400",cancelled:"bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-400"})[c]||"bg-gray-100 text-gray-800",S=c=>({completed:"已完成",failed:"失败",cancelled:"已取消"})[c]||c,$=c=>{g.info(`处理待办事项: ${c.title}`)},i=c=>{g.info(`查看任务详情: ${c.title}`)},v=()=>{g.info("跳转到任务历史页面")},y=c=>{switch(c.action){case"create-collection":g.info("跳转到商品采集页面");break;case"batch-process":g.info("打开批量处理工具");break;case"view-gallery":g.info("跳转到图库管理页面");break;case"system-settings":g.info("跳转到系统设置页面");break;default:g.info(`执行操作: ${c.name}`)}},l=()=>{g.success("任务列表已刷新")};return(c,r)=>(s(),a("div",kr,[e("div",br,[r[0]||(r[0]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"常用事务",-1)),e("button",{onClick:l,class:"text-sm text-gray-500 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text"},[n(T(O),{class:"w-4 h-4"})])]),e("div",vr,[e("div",yr,[r[1]||(r[1]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary"},"待处理事项",-1)),e("span",hr,o(u.value.length)+" 项",1)]),u.value.length===0?(s(),a("div",fr,[n(T(K),{class:"w-8 h-8 text-green-500 mx-auto mb-2"}),r[2]||(r[2]=e("p",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"暂无待处理事项",-1))])):(s(),a("div",_r,[(s(!0),a(C,null,M(u.value,d=>(s(),a("div",{key:d.id,class:"flex items-center justify-between p-3 bg-gray-50 dark:bg-dark-border rounded-lg hover:bg-gray-100 dark:hover:bg-dark-card transition-colors cursor-pointer",onClick:P=>$(d)},[e("div",$r,[e("div",{class:j(["w-2 h-2 rounded-full",x(d.status)])},null,2),e("div",null,[e("div",Cr,o(d.title),1),e("div",Mr,o(d.type)+" · "+o(d.createTime),1)])]),n(T(E),{class:"w-4 h-4 text-gray-400"})],8,wr))),128))]))]),e("div",jr,[e("div",{class:"flex items-center justify-between mb-3"},[r[3]||(r[3]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary"},"最近任务",-1)),e("button",{onClick:v,class:"text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"}," 查看全部 ")]),e("div",Tr,[(s(!0),a(C,null,M(f.value,d=>(s(),a("div",{key:d.id,class:"flex items-center justify-between p-2 hover:bg-gray-50 dark:hover:bg-dark-border rounded-lg transition-colors"},[e("div",Vr,[(s(),B(A(U(d.type)),{class:"w-4 h-4 text-gray-600 dark:text-dark-text-secondary"})),e("div",null,[e("div",Br,o(d.title),1),e("div",Ar,o(d.finishTime),1)])]),e("div",Ur,[e("span",{class:j(["inline-flex items-center px-2 py-0.5 rounded text-xs font-medium",V(d.status)])},o(S(d.status)),3),e("button",{onClick:P=>i(d),class:"text-xs text-gray-500 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text"}," 详情 ",8,Sr)])]))),128))])]),e("div",Dr,[r[4]||(r[4]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-3"},"快速操作",-1)),e("div",Ir,[(s(!0),a(C,null,M(k.value,d=>(s(),a("button",{key:d.id,onClick:P=>y(d),class:"flex items-center justify-center p-3 text-sm font-medium text-gray-700 dark:text-dark-text-secondary bg-gray-50 dark:bg-dark-border hover:bg-gray-100 dark:hover:bg-dark-card rounded-lg transition-colors"},[(s(),B(A(d.icon),{class:"w-4 h-4 mr-2"})),L(" "+o(d.name),1)],8,zr))),128))])]),e("div",Pr,[r[8]||(r[8]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-3"},"今日统计",-1)),e("div",Lr,[e("div",null,[e("div",qr,o(b.value.completed),1),r[5]||(r[5]=e("div",{class:"text-xs text-gray-500 dark:text-dark-text-secondary"},"已完成",-1))]),e("div",null,[e("div",Fr,o(b.value.processing),1),r[6]||(r[6]=e("div",{class:"text-xs text-gray-500 dark:text-dark-text-secondary"},"处理中",-1))]),e("div",null,[e("div",Hr,o(b.value.pending),1),r[7]||(r[7]=e("div",{class:"text-xs text-gray-500 dark:text-dark-text-secondary"},"待处理",-1))])])])]))}}),Nr={class:"bg-white dark:bg-dark-card rounded-xl p-6 shadow-sm border border-gray-100 dark:border-dark-border"},Zr={class:"flex items-center justify-between mb-6"},Er={class:"grid grid-cols-1 gap-3 mb-6"},Gr=["onClick"],Or={class:"flex items-center space-x-3"},Qr={class:"text-left"},Jr={class:"font-medium"},Kr={class:"text-sm opacity-90"},Wr={class:"mb-6"},Xr={class:"grid grid-cols-2 gap-2"},Yr=["onClick"],es={class:"text-xs font-medium text-gray-700 dark:text-dark-text-secondary"},ts={class:"mb-6"},rs={class:"space-y-2"},ss=["onClick"],as={class:"flex items-center space-x-3"},os={class:"text-left"},ns={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},ds={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},ls={class:"border-t border-gray-200 dark:border-dark-border pt-4"},is={class:"grid grid-cols-3 gap-2"},cs=["onClick","title"],us={class:"text-xs text-gray-600 dark:text-dark-text-secondary"},gs={class:"space-y-4"},xs={class:"grid grid-cols-2 gap-2"},ms=["value"],ps={class:"text-sm text-gray-700 dark:text-dark-text-secondary"},ks={class:"flex items-center space-x-2 p-2 bg-gray-50 dark:bg-dark-border rounded-lg cursor-move"},bs={class:"text-sm text-gray-700 dark:text-dark-text-secondary"},vs={class:"flex justify-end space-x-3"},ys=N({__name:"QuickActions",setup(w){const u=p(!1),f=p([]),k=p([{id:"new-collection",name:"新建采集任务",description:"快速创建商品采集任务",icon:Q,action:"create-collection",gradient:"from-blue-500 to-blue-600"},{id:"batch-process",name:"批量处理图片",description:"智能裁图、抠图等批量操作",icon:H,action:"batch-image-process",gradient:"from-green-500 to-green-600"}]),b=p([{id:"gallery",name:"图库",description:"管理图片资源",icon:H,action:"open-gallery"},{id:"products",name:"商品",description:"商品管理",icon:R,action:"open-products"},{id:"titles",name:"标题",description:"标题生成",icon:J,action:"open-titles"},{id:"listing",name:"刊登",description:"批量刊登",icon:se,action:"open-listing"}]),x=p([{id:"batch-export",name:"批量导出数据",description:"导出选中的任务数据",icon:be,action:"batch-export"},{id:"batch-import",name:"批量导入数据",description:"从文件导入数据",icon:he,action:"batch-import"},{id:"batch-delete",name:"批量清理任务",description:"清理过期或失败的任务",icon:Be,action:"batch-cleanup"}]),U=p([{id:"reports",name:"报表",description:"查看数据报表",icon:de,action:"view-reports"},{id:"settings",name:"设置",description:"系统设置",icon:je,action:"open-settings"},{id:"security",name:"安全",description:"安全中心",icon:ve,action:"security-center"},{id:"notifications",name:"通知",description:"消息通知",icon:_e,action:"view-notifications"},{id:"backup",name:"备份",description:"数据备份",icon:Ve,action:"data-backup"},{id:"logs",name:"日志",description:"系统日志",icon:ne,action:"view-logs"}]),V=p([...k.value,...b.value,...x.value,...U.value]),S=ee({get:()=>V.value.filter(r=>f.value.includes(r.id)),set:r=>{f.value=r.map(d=>d.id)}}),$=r=>{switch(r.action){case"create-collection":g.info("跳转到商品采集页面");break;case"batch-image-process":g.info("打开批量图片处理工具");break;default:g.info(`执行操作: ${r.name}`)}},i=r=>{switch(r.action){case"open-gallery":g.info("跳转到图库管理页面");break;case"open-products":g.info("跳转到商品管理页面");break;case"open-titles":g.info("跳转到标题生成页面");break;case"open-listing":g.info("跳转到批量刊登页面");break;default:g.info(`打开工具: ${r.name}`)}},v=r=>{switch(r.action){case"batch-export":g.info("开始批量导出数据");break;case"batch-import":g.info("打开批量导入工具");break;case"batch-cleanup":g.info("开始清理过期任务");break;default:g.info(`执行批量操作: ${r.name}`)}},y=r=>{switch(r.action){case"view-reports":g.info("跳转到数据报表页面");break;case"open-settings":g.info("跳转到系统设置页面");break;case"security-center":g.info("跳转到安全中心");break;case"view-notifications":g.info("打开消息通知");break;case"data-backup":g.info("开始数据备份");break;case"view-logs":g.info("查看系统日志");break;default:g.info(`执行系统工具: ${r.name}`)}},l=()=>{f.value=[...k.value.map(r=>r.id),...b.value.slice(0,4).map(r=>r.id)],u.value=!0},c=()=>{g.success("快捷操作设置已保存"),u.value=!1};return(r,d)=>{const P=I("el-button"),h=I("el-dialog");return s(),a("div",Nr,[e("div",Zr,[d[4]||(d[4]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"快捷操作",-1)),e("button",{onClick:l,class:"text-sm text-gray-500 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text"},[n(T(ae),{class:"w-4 h-4"})])]),e("div",Er,[(s(!0),a(C,null,M(k.value,t=>(s(),a("button",{key:t.id,onClick:D=>$(t),class:j(["flex items-center justify-between p-4 bg-gradient-to-r rounded-lg text-white transition-all duration-200 hover:shadow-lg transform hover:scale-[1.02]",t.gradient])},[e("div",Or,[(s(),B(A(t.icon),{class:"w-6 h-6"})),e("div",Qr,[e("div",Jr,o(t.name),1),e("div",Kr,o(t.description),1)])]),n(T(E),{class:"w-5 h-5 opacity-70"})],10,Gr))),128))]),e("div",Wr,[d[5]||(d[5]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-3"},"常用工具",-1)),e("div",Xr,[(s(!0),a(C,null,M(b.value,t=>(s(),a("button",{key:t.id,onClick:D=>i(t),class:"flex flex-col items-center p-3 bg-gray-50 dark:bg-dark-border hover:bg-gray-100 dark:hover:bg-dark-card rounded-lg transition-colors"},[(s(),B(A(t.icon),{class:"w-6 h-6 text-gray-600 dark:text-dark-text-secondary mb-2"})),e("span",es,o(t.name),1)],8,Yr))),128))])]),e("div",ts,[d[6]||(d[6]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-3"},"批量操作",-1)),e("div",rs,[(s(!0),a(C,null,M(x.value,t=>(s(),a("button",{key:t.id,onClick:D=>v(t),class:"w-full flex items-center justify-between p-3 bg-gray-50 dark:bg-dark-border hover:bg-gray-100 dark:hover:bg-dark-card rounded-lg transition-colors"},[e("div",as,[(s(),B(A(t.icon),{class:"w-5 h-5 text-gray-600 dark:text-dark-text-secondary"})),e("div",os,[e("div",ns,o(t.name),1),e("div",ds,o(t.description),1)])]),n(T(E),{class:"w-4 h-4 text-gray-400"})],8,ss))),128))])]),e("div",ls,[d[7]||(d[7]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-3"},"系统工具",-1)),e("div",is,[(s(!0),a(C,null,M(U.value,t=>(s(),a("button",{key:t.id,onClick:D=>y(t),class:"flex flex-col items-center p-2 hover:bg-gray-50 dark:hover:bg-dark-border rounded-lg transition-colors",title:t.description},[(s(),B(A(t.icon),{class:"w-5 h-5 text-gray-600 dark:text-dark-text-secondary mb-1"})),e("span",us,o(t.name),1)],8,cs))),128))])]),n(h,{modelValue:u.value,"onUpdate:modelValue":d[3]||(d[3]=t=>u.value=t),title:"自定义快捷操作",width:"600px"},{footer:_(()=>[e("div",vs,[n(P,{onClick:d[2]||(d[2]=t=>u.value=!1)},{default:_(()=>d[11]||(d[11]=[L("取消")])),_:1,__:[11]}),n(P,{type:"primary",onClick:c},{default:_(()=>d[12]||(d[12]=[L(" 保存设置 ")])),_:1,__:[12]})])]),default:_(()=>[e("div",gs,[e("div",null,[d[8]||(d[8]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},"选择要显示的操作",-1)),e("div",xs,[(s(!0),a(C,null,M(V.value,t=>(s(),a("label",{key:t.id,class:"flex items-center space-x-2 p-2 border rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-dark-border"},[me(e("input",{type:"checkbox",value:t.id,"onUpdate:modelValue":d[0]||(d[0]=D=>f.value=D),class:"text-blue-600 focus:ring-blue-500"},null,8,ms),[[pe,f.value]]),(s(),B(A(t.icon),{class:"w-4 h-4 text-gray-600 dark:text-dark-text-secondary"})),e("span",ps,o(t.name),1)]))),128))])]),e("div",null,[d[9]||(d[9]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},"操作顺序",-1)),d[10]||(d[10]=e("p",{class:"text-xs text-gray-500 dark:text-dark-text-secondary mb-2"},"拖拽调整操作顺序",-1)),n(T(ke),{modelValue:S.value,"onUpdate:modelValue":d[1]||(d[1]=t=>S.value=t),"item-key":"id",class:"space-y-2"},{item:_(({element:t})=>[e("div",ks,[n(T(fe),{class:"w-4 h-4 text-gray-400"}),(s(),B(A(t.icon),{class:"w-4 h-4 text-gray-600 dark:text-dark-text-secondary"})),e("span",bs,o(t.name),1)])]),_:1},8,["modelValue"])])])]),_:1},8,["modelValue"])])}}}),hs={class:"p-6 bg-gray-50 dark:bg-dark-bg min-h-screen"},fs={class:"flex items-center justify-between mb-8"},_s={class:"text-gray-600 dark:text-dark-text-secondary mt-1"},ws={class:"flex items-center space-x-4"},$s=["loading"],Cs={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Ms={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},js={class:"flex items-center justify-between"},Ts={class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},Vs={class:"text-2xl font-bold text-gray-900 dark:text-dark-text mt-1"},Bs={class:"flex items-center mt-2"},As={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},Us={class:"lg:col-span-2 space-y-8"},Ss={class:"space-y-8"},Zs=N({__name:"dashboard",setup(w){const u=p(!1),f=p(""),k=p(""),b=p({nickname:"张三",avatar:"/default-avatar.png"}),x=p([{id:"total-tasks",label:"今日任务",value:"156",change:"+12.5%",trend:"up",icon:de,iconBg:"bg-blue-100 dark:bg-blue-900/20",iconColor:"text-blue-600 dark:text-blue-400"},{id:"processing",label:"处理中",value:"23",change:"+8.2%",trend:"up",icon:R,iconBg:"bg-amber-100 dark:bg-amber-900/20",iconColor:"text-amber-600 dark:text-amber-400"},{id:"completed",label:"已完成",value:"128",change:"+15.3%",trend:"up",icon:oe,iconBg:"bg-green-100 dark:bg-green-900/20",iconColor:"text-green-600 dark:text-green-400"},{id:"revenue",label:"今日收入",value:"¥2,456",change:"-3.1%",trend:"down",icon:ye,iconBg:"bg-purple-100 dark:bg-purple-900/20",iconColor:"text-purple-600 dark:text-purple-400"}]),U=i=>i.toLocaleString("zh-CN",{year:"numeric",month:"long",day:"numeric",weekday:"long",hour:"2-digit",minute:"2-digit"}),V=()=>{const i=new Date;f.value=U(i),k.value=i.toLocaleTimeString("zh-CN")},S=async()=>{u.value=!0;try{await new Promise(i=>setTimeout(i,1e3)),V(),x.value.forEach(i=>{const v=parseFloat((Math.random()*20-10).toFixed(1));i.change=`${v>0?"+":""}${v}%`,i.trend=v>0?"up":"down"})}finally{u.value=!1}};let $;return G(()=>{V(),$=setInterval(V,6e4)}),te(()=>{$&&clearInterval($)}),(i,v)=>(s(),a("div",hs,[e("div",fs,[e("div",null,[v[0]||(v[0]=e("h1",{class:"text-3xl font-bold text-gray-900 dark:text-dark-text"},"驾驶舱",-1)),e("p",_s,o(f.value)+" · 欢迎回来，"+o(b.value.nickname),1)]),e("div",ws,[e("button",{onClick:S,loading:u.value,class:"inline-flex items-center px-4 py-2 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-900 dark:hover:text-dark-text bg-white dark:bg-dark-card hover:bg-gray-50 dark:hover:bg-dark-border rounded-lg shadow-sm transition-all duration-200"},[n(T(O),{class:j(["w-4 h-4 mr-2",{"animate-spin":u.value}])},null,8,["class"]),v[1]||(v[1]=L(" 刷新数据 "))],8,$s),e("div",Cs," 最后更新: "+o(k.value),1)])]),e("div",Ms,[(s(!0),a(C,null,M(x.value,y=>(s(),a("div",{key:y.id,class:"bg-white dark:bg-dark-card rounded-xl p-6 shadow-sm border border-gray-100 dark:border-dark-border hover:shadow-md transition-all duration-200"},[e("div",js,[e("div",null,[e("p",Ts,o(y.label),1),e("p",Vs,o(y.value),1),e("div",Bs,[(s(),B(A(y.trend==="up"?T($e):T(we)),{class:j(["w-4 h-4 mr-1",[y.trend==="up"?"text-green-500":"text-red-500"]])},null,8,["class"])),e("span",{class:j(["text-sm font-medium",[y.trend==="up"?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"]])},o(y.change),3),v[2]||(v[2]=e("span",{class:"text-sm text-gray-500 dark:text-dark-text-secondary ml-1"},"vs 昨日",-1))])]),e("div",{class:j(["w-12 h-12 rounded-lg flex items-center justify-center",y.iconBg])},[(s(),B(A(y.icon),{class:j(["w-6 h-6",y.iconColor])},null,8,["class"]))],2)])]))),128))]),e("div",As,[e("div",Us,[n(pt),n(Et)]),e("div",Ss,[n(pr),n(Rr),n(ys)])])]))}});export{Zs as default};
