import{d as J,e as b,r as h,f as Q,q as z,j as d,h as g,a as o,c as v,B as f,g as a,t as k,k as R,n as C,F as W,E as V,o as i}from"./index-ByC_LaEp.js";const X={class:"space-y-4"},Y={class:"flex justify-between items-center"},Z={class:"flex space-x-4"},ee={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},te={class:"border border-gray-200 dark:border-dark-border rounded-lg p-4 max-h-96 overflow-y-auto"},oe={class:"grid grid-cols-6 gap-4"},re=["onClick"],le={class:"relative"},ae=["src","alt"],se={class:"text-xs text-gray-600 dark:text-dark-text-secondary mt-1 truncate"},ne={key:0,class:"text-center py-8"},ue={key:0,class:"flex justify-center"},de={class:"flex justify-between"},ie={class:"flex space-x-3"},me=J({__name:"GallerySelectDialog",props:{modelValue:{type:Boolean},themeColor:{}},emits:["update:modelValue","select"],setup(M,{emit:E}){const B=M,I=E,x=b({get:()=>B.modelValue,set:t=>I("update:modelValue",t)}),c=h(""),p=h(""),l=h([]),m=h(1),_=h(24),w=b(()=>B.themeColor||"blue"),S=h([{id:1,name:"商品图片_001.jpg",thumbnail:"https://picsum.photos/200/200?random=1",url:"https://picsum.photos/400/400?random=1",category:"product"},{id:2,name:"商品图片_002.jpg",thumbnail:"https://picsum.photos/200/200?random=2",url:"https://picsum.photos/400/400?random=2",category:"product"},{id:3,name:"背景图片_001.jpg",thumbnail:"https://picsum.photos/200/200?random=3",url:"https://picsum.photos/400/400?random=3",category:"background"},{id:4,name:"素材图片_001.jpg",thumbnail:"https://picsum.photos/200/200?random=4",url:"https://picsum.photos/400/400?random=4",category:"material"},{id:5,name:"商品图片_003.jpg",thumbnail:"https://picsum.photos/200/200?random=5",url:"https://picsum.photos/400/400?random=5",category:"product"},{id:6,name:"商品图片_004.jpg",thumbnail:"https://picsum.photos/200/200?random=6",url:"https://picsum.photos/400/400?random=6",category:"product"},{id:7,name:"背景图片_002.jpg",thumbnail:"https://picsum.photos/200/200?random=7",url:"https://picsum.photos/400/400?random=7",category:"background"},{id:8,name:"素材图片_002.jpg",thumbnail:"https://picsum.photos/200/200?random=8",url:"https://picsum.photos/400/400?random=8",category:"material"},{id:9,name:"商品图片_005.jpg",thumbnail:"https://picsum.photos/200/200?random=9",url:"https://picsum.photos/400/400?random=9",category:"product"},{id:10,name:"商品图片_006.jpg",thumbnail:"https://picsum.photos/200/200?random=10",url:"https://picsum.photos/400/400?random=10",category:"product"},{id:11,name:"背景图片_003.jpg",thumbnail:"https://picsum.photos/200/200?random=11",url:"https://picsum.photos/400/400?random=11",category:"background"},{id:12,name:"素材图片_003.jpg",thumbnail:"https://picsum.photos/200/200?random=12",url:"https://picsum.photos/400/400?random=12",category:"material"}]),s=b(()=>{let t=S.value;p.value&&(t=t.filter(n=>n.category===p.value)),c.value&&(t=t.filter(n=>n.name.toLowerCase().includes(c.value.toLowerCase())));const e=(m.value-1)*_.value,u=e+_.value;return t.slice(e,u)}),N=b(()=>{let t=S.value;return p.value&&(t=t.filter(e=>e.category===p.value)),c.value&&(t=t.filter(e=>e.name.toLowerCase().includes(c.value.toLowerCase()))),t.length}),L=b(()=>s.value.length>0&&s.value.every(t=>y(t))),y=t=>l.value.some(e=>e.id===t.id),U=t=>{const e=l.value.findIndex(u=>u.id===t.id);e>-1?l.value.splice(e,1):l.value.push(t)},P=()=>{L.value?s.value.forEach(t=>{const e=l.value.findIndex(u=>u.id===t.id);e>-1&&l.value.splice(e,1)}):s.value.forEach(t=>{y(t)||l.value.push(t)})},A=()=>{m.value=1},D=()=>{m.value=1},F=t=>{m.value=t},$=()=>{I("select",l.value),l.value=[]},q=t=>{const e=w.value;if(y(t))switch(e){case"pink":return"border-pink-500";case"purple":return"border-purple-500";case"yellow":return"border-yellow-500";case"blue":return"border-blue-500";default:return"border-blue-500"}else switch(e){case"pink":return"border-gray-200 dark:border-dark-border hover:border-pink-300";case"purple":return"border-gray-200 dark:border-dark-border hover:border-purple-300";case"yellow":return"border-gray-200 dark:border-dark-border hover:border-yellow-300";case"blue":return"border-gray-200 dark:border-dark-border hover:border-blue-300";default:return"border-gray-200 dark:border-dark-border hover:border-blue-300"}},G=()=>{switch(w.value){case"pink":return"absolute inset-0 bg-pink-500 bg-opacity-20 rounded-lg flex items-center justify-center";case"purple":return"absolute inset-0 bg-purple-500 bg-opacity-20 rounded-lg flex items-center justify-center";case"yellow":return"absolute inset-0 bg-yellow-500 bg-opacity-20 rounded-lg flex items-center justify-center";case"blue":return"absolute inset-0 bg-blue-500 bg-opacity-20 rounded-lg flex items-center justify-center";default:return"absolute inset-0 bg-blue-500 bg-opacity-20 rounded-lg flex items-center justify-center"}},H=()=>{switch(w.value){case"pink":return"w-6 h-6 bg-pink-500 rounded-full flex items-center justify-center";case"purple":return"w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center";case"yellow":return"w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center";case"blue":return"w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center";default:return"w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center"}};return Q(()=>{}),(t,e)=>{const u=g("el-input"),n=g("el-option"),K=g("el-select"),O=g("el-pagination"),j=g("el-button"),T=g("el-dialog");return i(),z(T,{modelValue:x.value,"onUpdate:modelValue":e[4]||(e[4]=r=>x.value=r),title:"从图库选择图片",width:"1000px","align-center":""},{footer:d(()=>[o("div",de,[s.value.length>0?(i(),z(j,{key:0,onClick:P},{default:d(()=>[V(k(L.value?"取消全选":"全选当页"),1)]),_:1})):f("",!0),o("div",ie,[a(j,{onClick:e[3]||(e[3]=r=>x.value=!1)},{default:d(()=>e[9]||(e[9]=[V("取消")])),_:1,__:[9]}),a(j,{type:"primary",onClick:$,disabled:l.value.length===0},{default:d(()=>[V(" 确认选择 ("+k(l.value.length)+") ",1)]),_:1},8,["disabled"])])])]),default:d(()=>[o("div",X,[o("div",Y,[o("div",Z,[a(u,{modelValue:c.value,"onUpdate:modelValue":e[0]||(e[0]=r=>c.value=r),placeholder:"搜索图片...",style:{width:"300px"},onInput:A},{prefix:d(()=>e[5]||(e[5]=[o("svg",{class:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),_:1},8,["modelValue"]),a(K,{modelValue:p.value,"onUpdate:modelValue":e[1]||(e[1]=r=>p.value=r),placeholder:"选择分类",style:{width:"150px"},onChange:D},{default:d(()=>[a(n,{label:"全部分类",value:""}),a(n,{label:"商品图片",value:"product"}),a(n,{label:"背景图片",value:"background"}),a(n,{label:"素材图片",value:"material"})]),_:1},8,["modelValue"])]),o("div",ee," 已选择 "+k(l.value.length)+" 张图片 ",1)]),o("div",te,[o("div",oe,[(i(!0),v(W,null,R(s.value,r=>(i(),v("div",{key:r.id,class:"relative group cursor-pointer",onClick:ce=>U(r)},[o("div",le,[o("img",{src:r.thumbnail,alt:r.name,class:C(["w-full h-24 object-cover rounded-lg border-2 transition-all duration-200",q(r)])},null,10,ae),y(r)?(i(),v("div",{key:0,class:C(G())},[o("div",{class:C(H())},e[6]||(e[6]=[o("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1)]),2)],2)):f("",!0),e[7]||(e[7]=o("div",{class:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 rounded-lg transition-all duration-200"},null,-1))]),o("p",se,k(r.name),1)],8,re))),128))]),s.value.length===0?(i(),v("div",ne,e[8]||(e[8]=[o("svg",{class:"w-12 h-12 text-gray-400 dark:text-dark-text-secondary mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),o("p",{class:"text-gray-500 dark:text-dark-text-secondary"},"没有找到匹配的图片",-1)]))):f("",!0)]),s.value.length>0?(i(),v("div",ue,[a(O,{"current-page":m.value,"onUpdate:currentPage":e[2]||(e[2]=r=>m.value=r),"page-size":_.value,total:N.value,layout:"prev, pager, next",onCurrentChange:F},null,8,["current-page","page-size","total"])])):f("",!0)])]),_:1},8,["modelValue"])}}});export{me as _};
