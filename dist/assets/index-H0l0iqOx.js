import{d as re,e as Q,r as x,A as le,m as me,c as n,F as L,g as u,a as e,h,j as p,B as W,E as _,w as Y,l as pe,H as J,o as s,_ as oe,f as de,n as se,k as E,t as b,q as Z,U as ge,V as ve,M as ke,p as be,b as ae,v as xe,Q as fe}from"./index-ByC_LaEp.js";const he={class:"space-y-6"},ye={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},we={key:0,class:"relative"},_e=["src"],$e={key:1,class:"h-40 flex flex-col items-center justify-center"},Ve={key:0,class:"relative"},Ce=["src"],Ie={key:1,class:"h-40 flex flex-col items-center justify-center"},je={key:0,class:"relative"},Ue=["src"],ze={key:1,class:"h-40 flex flex-col items-center justify-center"},Se={key:0,class:"bg-gray-50 dark:bg-dark-card rounded-lg p-6"},Me={class:"relative bg-white dark:bg-dark-surface rounded-lg border border-gray-200 dark:border-dark-border overflow-hidden",style:{height:"400px"}},Le=["src"],Ke=["src"],Be=["src"],Ae={class:"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4"},De={class:"mt-4 flex items-center space-x-4"},Pe={class:"flex space-x-2"},Ee={class:"flex justify-end space-x-3"},Re=re({__name:"CreateEffectDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","created"],setup(G,{emit:N}){const D=G,B=N,U=Q({get:()=>D.modelValue,set:t=>B("update:modelValue",t)}),$=x(!1),C=x(),z=x(),A=x("base"),i=le({baseImage:"",printImage:"",maskImage:""}),d=le({visible:!1,x:50,y:50,width:100,height:100,rotation:0}),o=le({isDragging:!1,isResizing:!1,isRotating:!1,startX:0,startY:0,startWidth:0,startHeight:0,startRotation:0,resizeDirection:""}),V=t=>{var r;A.value=t,(r=C.value)==null||r.click()},T=t=>{var j;const r=t.target,v=(j=r.files)==null?void 0:j[0];if(v){const g=new FileReader;g.onload=X=>{var ee;const w=(ee=X.target)==null?void 0:ee.result;A.value==="base"?i.baseImage=w:A.value==="print"?(i.printImage=w,d.visible=!0):A.value==="mask"&&(i.maskImage=w)},g.readAsDataURL(v)}r.value=""},M=t=>{t==="base"?i.baseImage="":t==="print"?(i.printImage="",d.visible=!1):t==="mask"&&(i.maskImage="")},R=t=>{o.isDragging=!0,o.startX=t.clientX-d.x,o.startY=t.clientY-d.y,document.addEventListener("mousemove",q),document.addEventListener("mouseup",y)},q=t=>{o.isDragging&&(d.x=Math.max(0,Math.min(400-d.width,t.clientX-o.startX)),d.y=Math.max(0,Math.min(400-d.height,t.clientY-o.startY)))},y=()=>{o.isDragging=!1,document.removeEventListener("mousemove",q),document.removeEventListener("mouseup",y)},l=t=>{o.isResizing=!0,o.resizeDirection=t,o.startWidth=d.width,o.startHeight=d.height,document.addEventListener("mousemove",F),document.addEventListener("mouseup",P)},F=t=>{if(o.isResizing){const r=t.movementX,v=t.movementY;o.resizeDirection.includes("e")&&(d.width=Math.max(10,d.width+r)),o.resizeDirection.includes("s")&&(d.height=Math.max(10,d.height+v))}},P=()=>{o.isResizing=!1,document.removeEventListener("mousemove",F),document.removeEventListener("mouseup",P)},O=t=>{o.isRotating=!0,o.startRotation=d.rotation,document.addEventListener("mousemove",k),document.addEventListener("mouseup",c)},k=t=>{o.isRotating&&(d.rotation+=t.movementX,d.rotation=Math.max(-180,Math.min(180,d.rotation)))},c=()=>{o.isRotating=!1,document.removeEventListener("mousemove",k),document.removeEventListener("mouseup",c)},K=()=>{d.x=50,d.y=50,d.width=100,d.height=100,d.rotation=0},I=()=>{d.x=(400-d.width)/2,d.y=(400-d.height)/2},a=()=>{$.value=!0,setTimeout(()=>{$.value=!1;const t="https://picsum.photos/400/400?random="+Date.now();B("created",t),J.success("效果图生成成功！"),m()},2e3)},m=()=>{i.baseImage="",i.printImage="",i.maskImage="",d.visible=!1,K()};return me(()=>{document.removeEventListener("mousemove",q),document.removeEventListener("mouseup",y),document.removeEventListener("mousemove",F),document.removeEventListener("mouseup",P),document.removeEventListener("mousemove",k),document.removeEventListener("mouseup",c)}),(t,r)=>{const v=h("el-input-number"),j=h("el-slider"),g=h("el-button"),X=h("el-dialog");return s(),n(L,null,[u(X,{modelValue:U.value,"onUpdate:modelValue":r[16]||(r[16]=w=>U.value=w),title:"创建效果图",width:"1200px","align-center":"",onClose:m},{footer:p(()=>[e("div",Ee,[u(g,{onClick:r[15]||(r[15]=w=>U.value=!1)},{default:p(()=>r[35]||(r[35]=[_("取消")])),_:1,__:[35]}),u(g,{type:"primary",onClick:a,loading:$.value,disabled:!i.baseImage||!i.printImage},{default:p(()=>r[36]||(r[36]=[_(" 生成效果图 ")])),_:1,__:[36]},8,["loading","disabled"])])]),default:p(()=>[e("div",he,[e("div",ye,[e("div",null,[r[19]||(r[19]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},[_(" 底图 "),e("span",{class:"text-red-500"},"*")],-1)),e("div",{class:"border-2 border-dashed border-gray-300 dark:border-dark-border rounded-lg p-4 text-center cursor-pointer hover:border-amber-500 dark:hover:border-amber-500 transition-colors",onClick:r[1]||(r[1]=w=>V("base"))},[i.baseImage?(s(),n("div",we,[e("img",{src:i.baseImage,alt:"底图",class:"w-full h-40 object-cover rounded-lg"},null,8,_e),e("button",{onClick:r[0]||(r[0]=Y(w=>M("base"),["stop"])),class:"absolute top-2 right-2 w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600"},r[17]||(r[17]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])):(s(),n("div",$e,r[18]||(r[18]=[e("svg",{class:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"mt-2 text-sm text-gray-500 dark:text-dark-text-secondary"},"点击上传底图",-1)])))])]),e("div",null,[r[22]||(r[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},[_(" 印刷图 "),e("span",{class:"text-red-500"},"*")],-1)),e("div",{class:"border-2 border-dashed border-gray-300 dark:border-dark-border rounded-lg p-4 text-center cursor-pointer hover:border-amber-500 dark:hover:border-amber-500 transition-colors",onClick:r[3]||(r[3]=w=>V("print"))},[i.printImage?(s(),n("div",Ve,[e("img",{src:i.printImage,alt:"印刷图",class:"w-full h-40 object-cover rounded-lg"},null,8,Ce),e("button",{onClick:r[2]||(r[2]=Y(w=>M("print"),["stop"])),class:"absolute top-2 right-2 w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600"},r[20]||(r[20]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])):(s(),n("div",Ie,r[21]||(r[21]=[e("svg",{class:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"mt-2 text-sm text-gray-500 dark:text-dark-text-secondary"},"点击上传印刷图",-1)])))])]),e("div",null,[r[25]||(r[25]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"}," 遮罩图 ",-1)),e("div",{class:"border-2 border-dashed border-gray-300 dark:border-dark-border rounded-lg p-4 text-center cursor-pointer hover:border-amber-500 dark:hover:border-amber-500 transition-colors",onClick:r[5]||(r[5]=w=>V("mask"))},[i.maskImage?(s(),n("div",je,[e("img",{src:i.maskImage,alt:"遮罩图",class:"w-full h-40 object-cover rounded-lg"},null,8,Ue),e("button",{onClick:r[4]||(r[4]=Y(w=>M("mask"),["stop"])),class:"absolute top-2 right-2 w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600"},r[23]||(r[23]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])):(s(),n("div",ze,r[24]||(r[24]=[e("svg",{class:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"mt-2 text-sm text-gray-500 dark:text-dark-text-secondary"},"点击上传遮罩图",-1)])))])])]),i.baseImage&&i.printImage?(s(),n("div",Se,[r[33]||(r[33]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-4"},"设置印刷区域",-1)),e("div",Me,[e("img",{src:i.baseImage,alt:"底图",class:"absolute inset-0 w-full h-full object-contain"},null,8,Le),d.visible?(s(),n("div",{key:0,ref_key:"printAreaRef",ref:z,class:"absolute border-2 border-amber-500 bg-amber-500 bg-opacity-20 cursor-move",style:pe({left:d.x+"px",top:d.y+"px",width:d.width+"px",height:d.height+"px",transform:`rotate(${d.rotation}deg)`}),onMousedown:R},[e("img",{src:i.printImage,alt:"印刷图",class:"w-full h-full object-cover opacity-80"},null,8,Ke),e("div",{class:"absolute -top-2 -left-2 w-4 h-4 bg-amber-500 rounded-full cursor-nw-resize",onMousedown:r[6]||(r[6]=Y(w=>l("nw"),["stop"]))},null,32),e("div",{class:"absolute -top-2 -right-2 w-4 h-4 bg-amber-500 rounded-full cursor-ne-resize",onMousedown:r[7]||(r[7]=Y(w=>l("ne"),["stop"]))},null,32),e("div",{class:"absolute -bottom-2 -left-2 w-4 h-4 bg-amber-500 rounded-full cursor-sw-resize",onMousedown:r[8]||(r[8]=Y(w=>l("sw"),["stop"]))},null,32),e("div",{class:"absolute -bottom-2 -right-2 w-4 h-4 bg-amber-500 rounded-full cursor-se-resize",onMousedown:r[9]||(r[9]=Y(w=>l("se"),["stop"]))},null,32),e("div",{class:"absolute -top-8 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-blue-500 rounded-full cursor-pointer",onMousedown:Y(O,["stop"])},null,32)],36)):W("",!0),i.maskImage?(s(),n("img",{key:1,src:i.maskImage,alt:"遮罩图",class:"absolute inset-0 w-full h-full object-contain pointer-events-none"},null,8,Be)):W("",!0)]),e("div",Ae,[e("div",null,[r[26]||(r[26]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-1"},"X位置",-1)),u(v,{modelValue:d.x,"onUpdate:modelValue":r[10]||(r[10]=w=>d.x=w),min:0,max:400,size:"small"},null,8,["modelValue"])]),e("div",null,[r[27]||(r[27]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-1"},"Y位置",-1)),u(v,{modelValue:d.y,"onUpdate:modelValue":r[11]||(r[11]=w=>d.y=w),min:0,max:400,size:"small"},null,8,["modelValue"])]),e("div",null,[r[28]||(r[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-1"},"宽度",-1)),u(v,{modelValue:d.width,"onUpdate:modelValue":r[12]||(r[12]=w=>d.width=w),min:10,max:400,size:"small"},null,8,["modelValue"])]),e("div",null,[r[29]||(r[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-1"},"高度",-1)),u(v,{modelValue:d.height,"onUpdate:modelValue":r[13]||(r[13]=w=>d.height=w),min:10,max:400,size:"small"},null,8,["modelValue"])])]),e("div",De,[e("div",null,[r[30]||(r[30]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-1"},"旋转角度",-1)),u(j,{modelValue:d.rotation,"onUpdate:modelValue":r[14]||(r[14]=w=>d.rotation=w),min:-180,max:180,step:1,style:{width:"200px"}},null,8,["modelValue"])]),e("div",Pe,[u(g,{onClick:K,size:"small"},{default:p(()=>r[31]||(r[31]=[_("重置")])),_:1,__:[31]}),u(g,{onClick:I,size:"small"},{default:p(()=>r[32]||(r[32]=[_("居中")])),_:1,__:[32]})])])])):W("",!0),r[34]||(r[34]=e("div",{class:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4"},[e("h4",{class:"text-sm font-medium text-blue-900 dark:text-blue-100 mb-2"},"图层说明："),e("ul",{class:"text-sm text-blue-800 dark:text-blue-200 space-y-1"},[e("li",null,[_("• "),e("strong",null,"底图"),_("：作为最底层的背景图片")]),e("li",null,[_("• "),e("strong",null,"印刷区域"),_("：可调整位置、大小和旋转角度的印刷图案")]),e("li",null,[_("• "),e("strong",null,"遮罩图"),_("：覆盖在最上层的遮罩效果")])])],-1))])]),_:1},8,["modelValue"]),e("input",{ref_key:"fileInputRef",ref:C,type:"file",accept:"image/*",style:{display:"none"},onChange:T},null,544)],64)}}}),ne=oe(Re,[["__scopeId","data-v-8393c181"]]),He={class:"space-y-6"},Te={class:"flex space-x-3"},Fe={class:"border border-gray-200 dark:border-dark-border rounded-lg overflow-hidden"},Xe={class:"flex border-b border-gray-200 dark:border-dark-border"},Ye={key:0,class:"p-4"},Ne={key:0,class:"grid grid-cols-5 gap-4"},qe=["onClick"],We={class:"relative"},Ge=["src","alt"],Qe={key:0,class:"absolute inset-0 bg-amber-500 bg-opacity-20 rounded-lg flex items-center justify-center"},Je={class:"text-xs text-gray-600 dark:text-dark-text-secondary mt-1 truncate"},Oe={key:1,class:"text-center py-12"},Ze={key:1,class:"p-4"},et={class:"flex justify-between items-center mb-4"},tt={class:"flex space-x-4"},rt={key:0,class:"grid grid-cols-5 gap-4"},ot=["onClick"],st={class:"relative"},lt=["src","alt"],at={key:0,class:"absolute inset-0 bg-amber-500 bg-opacity-20 rounded-lg flex items-center justify-center"},dt={class:"text-xs text-gray-600 dark:text-dark-text-secondary mt-1 truncate"},nt={key:1,class:"text-center py-12"},it={class:"flex justify-between"},ut={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},ct={class:"flex space-x-3"},ie=re({__name:"ImageSelectorDialog",props:{modelValue:{type:Boolean},title:{},multiple:{type:Boolean}},emits:["update:modelValue","select"],setup(G,{emit:N}){const D=G,B=N,U=Q({get:()=>D.modelValue,set:k=>B("update:modelValue",k)}),$=x(!1),C=x(!1),z=x([]),A=x([]),i=x([]),d=x(""),o=x(""),V=x([{id:1,name:"商品图片_001.jpg",url:"https://picsum.photos/400/400?random=1",category:"product"},{id:2,name:"商品图片_002.jpg",url:"https://picsum.photos/400/400?random=2",category:"product"},{id:3,name:"背景图片_001.jpg",url:"https://picsum.photos/400/400?random=3",category:"background"},{id:4,name:"素材图片_001.jpg",url:"https://picsum.photos/400/400?random=4",category:"material"},{id:5,name:"商品图片_003.jpg",url:"https://picsum.photos/400/400?random=5",category:"product"},{id:6,name:"商品图片_004.jpg",url:"https://picsum.photos/400/400?random=6",category:"product"},{id:7,name:"背景图片_002.jpg",url:"https://picsum.photos/400/400?random=7",category:"background"},{id:8,name:"素材图片_002.jpg",url:"https://picsum.photos/400/400?random=8",category:"material"}]),T=Q(()=>{let k=V.value;return o.value&&(k=k.filter(c=>c.category===o.value)),d.value&&(k=k.filter(c=>c.name.toLowerCase().includes(d.value.toLowerCase()))),k}),M=k=>i.value.some(c=>typeof k.id=="number"&&typeof c.id=="number"?c.id===k.id:c.url===k.url),R=k=>{if(!D.multiple){i.value=[k];return}const c=i.value.findIndex(K=>typeof k.id=="number"&&typeof K.id=="number"?K.id===k.id:K.url===k.url);c>-1?i.value.splice(c,1):i.value.push(k)},q=k=>{const c=new FileReader;c.onload=K=>{var m;const a={id:Date.now()+Math.random(),name:k.name,url:(m=K.target)==null?void 0:m.result,file:k.raw};A.value.push(a),D.multiple?i.value.push(a):i.value=[a]},c.readAsDataURL(k.raw)},y=k=>{},l=()=>{},F=()=>{},P=()=>{B("select",i.value.map(k=>k.url)),B("update:modelValue",!1),i.value=[]},O=k=>{const c={id:Date.now(),name:`效果图_${Date.now()}.jpg`,url:k};A.value.push(c),C.value=!1,D.multiple?i.value.push(c):i.value=[c],J.success("效果图创建成功")};return de(()=>{}),(k,c)=>{const K=h("el-button"),I=h("el-upload"),a=h("el-input"),m=h("el-option"),t=h("el-select"),r=h("el-dialog");return s(),n(L,null,[u(r,{modelValue:U.value,"onUpdate:modelValue":c[7]||(c[7]=v=>U.value=v),title:k.title||"选择图片",width:"900px","align-center":""},{footer:p(()=>[e("div",it,[e("div",ut," 已选择 "+b(i.value.length)+" 张图片 ",1),e("div",ct,[u(K,{onClick:c[6]||(c[6]=v=>U.value=!1)},{default:p(()=>c[19]||(c[19]=[_("取消")])),_:1,__:[19]}),u(K,{type:"primary",onClick:P,disabled:i.value.length===0},{default:p(()=>c[20]||(c[20]=[_(" 确认选择 ")])),_:1,__:[20]},8,["disabled"])])])]),default:p(()=>[e("div",He,[e("div",Te,[u(I,{ref:"uploadRef","file-list":z.value,"on-change":q,"on-remove":y,"auto-upload":!1,multiple:"",accept:"image/*","show-file-list":!1},{default:p(()=>[u(K,{type:"primary"},{default:p(()=>c[9]||(c[9]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})],-1),_(" 上传图片 ")])),_:1,__:[9]})]),_:1},8,["file-list"]),u(K,{onClick:c[0]||(c[0]=v=>$.value=!0)},{default:p(()=>c[10]||(c[10]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),_(" 从图库选择 ")])),_:1,__:[10]}),u(K,{onClick:c[1]||(c[1]=v=>C.value=!0)},{default:p(()=>c[11]||(c[11]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"})],-1),_(" 创建效果图 ")])),_:1,__:[11]})]),e("div",Fe,[e("div",Xe,[e("button",{onClick:c[2]||(c[2]=v=>$.value=!1),class:se(["px-4 py-2 text-sm font-medium",$.value?"text-gray-500 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text":"text-amber-600 dark:text-amber-400 border-b-2 border-amber-500"])}," 已上传图片 ",2),e("button",{onClick:c[3]||(c[3]=v=>$.value=!0),class:se(["px-4 py-2 text-sm font-medium",$.value?"text-amber-600 dark:text-amber-400 border-b-2 border-amber-500":"text-gray-500 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text"])}," 图库 ",2)]),$.value?(s(),n("div",Ze,[e("div",et,[e("div",tt,[u(a,{modelValue:d.value,"onUpdate:modelValue":c[4]||(c[4]=v=>d.value=v),placeholder:"搜索图片...",style:{width:"300px"},onInput:l},{prefix:p(()=>c[15]||(c[15]=[e("svg",{class:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),_:1},8,["modelValue"]),u(t,{modelValue:o.value,"onUpdate:modelValue":c[5]||(c[5]=v=>o.value=v),placeholder:"选择分类",style:{width:"150px"},onChange:F},{default:p(()=>[u(m,{label:"全部分类",value:""}),u(m,{label:"商品图片",value:"product"}),u(m,{label:"背景图片",value:"background"}),u(m,{label:"素材图片",value:"material"})]),_:1},8,["modelValue"])])]),T.value.length>0?(s(),n("div",rt,[(s(!0),n(L,null,E(T.value,v=>(s(),n("div",{key:v.id,class:"relative group cursor-pointer",onClick:j=>R(v)},[e("div",st,[e("img",{src:v.url,alt:v.name,class:se(["w-full h-32 object-cover rounded-lg border-2 transition-all duration-200",M(v)?"border-amber-500":"border-gray-200 dark:border-dark-border hover:border-amber-300"])},null,10,lt),M(v)?(s(),n("div",at,c[16]||(c[16]=[e("div",{class:"w-6 h-6 bg-amber-500 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})])],-1)]))):W("",!0),c[17]||(c[17]=e("div",{class:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 rounded-lg transition-all duration-200"},null,-1))]),e("p",dt,b(v.name),1)],8,ot))),128))])):(s(),n("div",nt,c[18]||(c[18]=[e("svg",{class:"w-12 h-12 text-gray-400 dark:text-dark-text-secondary mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"text-gray-500 dark:text-dark-text-secondary"},"没有找到匹配的图片",-1)])))])):(s(),n("div",Ye,[A.value.length>0?(s(),n("div",Ne,[(s(!0),n(L,null,E(A.value,(v,j)=>(s(),n("div",{key:j,class:"relative group",onClick:g=>R(v)},[e("div",We,[e("img",{src:v.url,alt:v.name,class:se(["w-full h-32 object-cover rounded-lg border-2 transition-all duration-200",M(v)?"border-amber-500":"border-gray-200 dark:border-dark-border hover:border-amber-300"])},null,10,Ge),M(v)?(s(),n("div",Qe,c[12]||(c[12]=[e("div",{class:"w-6 h-6 bg-amber-500 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})])],-1)]))):W("",!0),c[13]||(c[13]=e("div",{class:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 rounded-lg transition-all duration-200"},null,-1))]),e("p",Je,b(v.name),1)],8,qe))),128))])):(s(),n("div",Oe,c[14]||(c[14]=[e("svg",{class:"w-12 h-12 text-gray-400 dark:text-dark-text-secondary mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"text-gray-500 dark:text-dark-text-secondary"},"暂无上传图片，请点击上方按钮上传",-1)])))]))])])]),_:1},8,["modelValue","title"]),u(ne,{modelValue:C.value,"onUpdate:modelValue":c[8]||(c[8]=v=>C.value=v),onCreated:O},null,8,["modelValue"])],64)}}}),mt={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-6"},pt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},gt={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-6"},vt={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},kt={key:0,class:"relative"},bt=["src"],xt={class:"absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 flex items-center justify-center transition-all rounded-lg"},ft={key:1,class:"h-40 flex flex-col items-center justify-center"},ht={key:0,class:"relative"},yt={class:"grid grid-cols-2 gap-2 h-40"},wt=["src","alt"],_t=["onClick"],$t={key:0,class:"absolute bottom-2 right-2 bg-gray-800 bg-opacity-70 text-white text-xs px-2 py-1 rounded-full"},Vt={key:1,class:"h-40 flex flex-col items-center justify-center"},Ct={key:0,class:"relative"},It={class:"grid grid-cols-2 gap-2 h-40"},jt=["src","alt"],Ut=["onClick"],zt={key:0,class:"absolute bottom-2 right-2 bg-gray-800 bg-opacity-70 text-white text-xs px-2 py-1 rounded-full"},St={key:1,class:"h-40 flex flex-col items-center justify-center"},Mt={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-6"},Lt={class:"flex items-center justify-between mb-4"},Kt={key:0,class:"space-y-4 mb-6"},Bt={class:"flex-1"},At={key:1,class:"border border-gray-200 dark:border-dark-border rounded-lg overflow-hidden"},Dt={class:"min-w-full divide-y divide-gray-200 dark:divide-dark-border"},Pt={class:"bg-gray-100 dark:bg-dark-border"},Et={class:"bg-white dark:bg-dark-surface divide-y divide-gray-200 dark:divide-dark-border"},Rt={class:"px-4 py-3 text-sm"},Ht={class:"px-4 py-3 text-sm"},Tt={class:"px-4 py-3 text-sm"},Ft={class:"flex items-center"},Xt=["src"],Yt={key:2,class:"text-center py-8 text-gray-500 dark:text-dark-text-secondary"},Nt={key:3,class:"text-center py-8 text-gray-500 dark:text-dark-text-secondary"},qt={class:"flex justify-end space-x-3"},Wt=re({__name:"CreateProductDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(G,{emit:N}){const D=G,B=N,U=Q({get:()=>D.modelValue,set:m=>B("update:modelValue",m)}),$=x(),C=x(!1),z=x(!1),A=x(!1),i=x("cover"),d=Q(()=>({cover:"选择封面图",additional:"选择附图",sku:"选择SKU图"})[i.value]),o=x({name:"",price:0,coverImage:"",additionalImages:[],skuImages:[],skuAttributes:[]}),V=x({name:[{required:!0,message:"请输入商品标题",trigger:"blur"},{min:3,max:100,message:"长度在 3 到 100 个字符",trigger:"blur"}],price:[{required:!0,message:"请输入商品价格",trigger:"blur"}]}),T=Q(()=>{if(o.value.skuAttributes.length===0)return[];if(!o.value.skuAttributes.every(j=>j.values.length>0))return[];const t=(j,g=[],X=0,w=[])=>{if(X===j.length){w.push([...g]);return}for(let ee=0;ee<j[X].length;ee++)g[X]=j[X][ee],t(j,g,X+1,w);return w},r=o.value.skuAttributes.map(j=>j.values),v=t(r);return(v==null?void 0:v.map(j=>{const g={};return o.value.skuAttributes.forEach((X,w)=>{g[X.name]=j[w]}),{attributes:g,price:o.value.price,stock:100,image:""}}))||[]}),M=m=>{i.value=m,z.value=!0},R=m=>{i.value==="cover"?o.value.coverImage=m[0]:i.value==="additional"?o.value.additionalImages=[...o.value.additionalImages,...m]:i.value==="sku"&&(o.value.skuImages=[...o.value.skuImages,...m])},q=m=>{o.value.coverImage=""},y=m=>{o.value.additionalImages.splice(m,1)},l=m=>{o.value.skuImages.splice(m,1)},F=()=>{o.value.skuAttributes.push({name:"",values:[],inputVisible:!1,inputValue:""})},P=m=>{o.value.skuAttributes.splice(m,1)},O=m=>{o.value.skuAttributes[m].inputVisible=!0,ve(()=>{var t;(t=document.querySelector(".el-input__inner"))==null||t.focus()})},k=m=>{const t=o.value.skuAttributes[m].inputValue;t&&o.value.skuAttributes[m].values.push(t),o.value.skuAttributes[m].inputVisible=!1,o.value.skuAttributes[m].inputValue=""},c=(m,t)=>{o.value.skuAttributes[m].values.splice(t,1)},K=async()=>{$.value&&await $.value.validate((m,t)=>{m&&(C.value=!0,setTimeout(()=>{C.value=!1,J.success("商品创建成功！"),I(),B("success"),B("update:modelValue",!1)},1e3))})},I=()=>{$.value&&$.value.resetFields(),o.value={name:"",price:0,coverImage:"",additionalImages:[],skuImages:[],skuAttributes:[]}},a=()=>{J.success("特效创建成功！")};return(m,t)=>{const r=h("el-input"),v=h("el-form-item"),j=h("el-input-number"),g=h("el-button"),X=h("el-tag"),w=h("el-option"),ee=h("el-select"),ue=h("el-form"),ce=h("el-dialog");return s(),n(L,null,[u(ce,{modelValue:U.value,"onUpdate:modelValue":t[7]||(t[7]=f=>U.value=f),title:"创建商品",width:"1000px","align-center":"",onClose:I,class:"create-product-dialog"},{footer:p(()=>[e("div",qt,[u(g,{onClick:t[6]||(t[6]=f=>U.value=!1)},{default:p(()=>t[28]||(t[28]=[_("取消")])),_:1,__:[28]}),u(g,{type:"primary",onClick:K,loading:C.value},{default:p(()=>t[29]||(t[29]=[_("创建商品")])),_:1,__:[29]},8,["loading"])])]),default:p(()=>[u(ue,{ref_key:"formRef",ref:$,model:o.value,rules:V.value,"label-position":"top",class:"space-y-6"},{default:p(()=>[e("div",mt,[t[10]||(t[10]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-4"},"基本信息",-1)),e("div",pt,[u(v,{label:"商品标题",prop:"name"},{default:p(()=>[u(r,{modelValue:o.value.name,"onUpdate:modelValue":t[0]||(t[0]=f=>o.value.name=f),placeholder:"请输入商品标题",maxlength:"100","show-word-limit":""},null,8,["modelValue"])]),_:1}),u(v,{label:"商品价格",prop:"price"},{default:p(()=>[u(j,{modelValue:o.value.price,"onUpdate:modelValue":t[1]||(t[1]=f=>o.value.price=f),min:0,precision:2,step:.1,style:{width:"100%"}},null,8,["modelValue"])]),_:1})])]),e("div",gt,[t[20]||(t[20]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-4"},"商品图片",-1)),e("div",vt,[e("div",null,[t[13]||(t[13]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},"封面图",-1)),e("div",{class:"border-2 border-dashed border-gray-300 dark:border-dark-border rounded-lg p-4 text-center cursor-pointer hover:border-amber-500 dark:hover:border-amber-500 transition-colors",onClick:t[3]||(t[3]=f=>M("cover"))},[o.value.coverImage?(s(),n("div",kt,[e("img",{src:o.value.coverImage,alt:"封面图",class:"w-full h-40 object-cover rounded-lg"},null,8,bt),e("div",xt,[e("button",{onClick:t[2]||(t[2]=Y(f=>q("cover"),["stop"])),class:"absolute top-2 right-2 w-8 h-8 bg-red-500 text-white rounded-full opacity-0 hover:opacity-100 flex items-center justify-center"},t[11]||(t[11]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])])):(s(),n("div",ft,t[12]||(t[12]=[e("svg",{class:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"mt-2 text-sm text-gray-500 dark:text-dark-text-secondary"},"点击上传封面图",-1)])))])]),e("div",null,[t[16]||(t[16]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},"附图（多张）",-1)),e("div",{class:"border-2 border-dashed border-gray-300 dark:border-dark-border rounded-lg p-4 text-center cursor-pointer hover:border-amber-500 dark:hover:border-amber-500 transition-colors",onClick:t[4]||(t[4]=f=>M("additional"))},[o.value.additionalImages.length>0?(s(),n("div",ht,[e("div",yt,[(s(!0),n(L,null,E(o.value.additionalImages.slice(0,4),(f,H)=>(s(),n("div",{key:H,class:"relative"},[e("img",{src:f,alt:`附图${H+1}`,class:"w-full h-full object-cover rounded-lg"},null,8,wt),e("button",{onClick:Y(S=>y(H),["stop"]),class:"absolute top-1 right-1 w-6 h-6 bg-red-500 text-white rounded-full opacity-0 hover:opacity-100 flex items-center justify-center"},t[14]||(t[14]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,_t)]))),128))]),o.value.additionalImages.length>4?(s(),n("div",$t," +"+b(o.value.additionalImages.length-4),1)):W("",!0)])):(s(),n("div",Vt,t[15]||(t[15]=[e("svg",{class:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"mt-2 text-sm text-gray-500 dark:text-dark-text-secondary"},"点击上传附图",-1)])))])]),e("div",null,[t[19]||(t[19]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},"SKU图",-1)),e("div",{class:"border-2 border-dashed border-gray-300 dark:border-dark-border rounded-lg p-4 text-center cursor-pointer hover:border-amber-500 dark:hover:border-amber-500 transition-colors",onClick:t[5]||(t[5]=f=>M("sku"))},[o.value.skuImages.length>0?(s(),n("div",Ct,[e("div",It,[(s(!0),n(L,null,E(o.value.skuImages.slice(0,4),(f,H)=>(s(),n("div",{key:H,class:"relative"},[e("img",{src:f,alt:`SKU图${H+1}`,class:"w-full h-full object-cover rounded-lg"},null,8,jt),e("button",{onClick:Y(S=>l(H),["stop"]),class:"absolute top-1 right-1 w-6 h-6 bg-red-500 text-white rounded-full opacity-0 hover:opacity-100 flex items-center justify-center"},t[17]||(t[17]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,Ut)]))),128))]),o.value.skuImages.length>4?(s(),n("div",zt," +"+b(o.value.skuImages.length-4),1)):W("",!0)])):(s(),n("div",St,t[18]||(t[18]=[e("svg",{class:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"mt-2 text-sm text-gray-500 dark:text-dark-text-secondary"},"点击上传SKU图",-1)])))])])])]),e("div",Mt,[e("div",Lt,[t[22]||(t[22]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text"},"SKU设置",-1)),u(g,{type:"primary",onClick:F,size:"small"},{default:p(()=>t[21]||(t[21]=[_("添加属性")])),_:1,__:[21]})]),o.value.skuAttributes.length>0?(s(),n("div",Kt,[(s(!0),n(L,null,E(o.value.skuAttributes,(f,H)=>(s(),n("div",{key:H,class:"flex items-start space-x-4"},[u(r,{modelValue:f.name,"onUpdate:modelValue":S=>f.name=S,placeholder:"属性名称，如：颜色、尺码",style:{width:"200px"}},null,8,["modelValue","onUpdate:modelValue"]),e("div",Bt,[(s(!0),n(L,null,E(f.values,(S,te)=>(s(),Z(X,{key:te,closable:"",onClose:Yo=>c(H,te),class:"mr-2 mb-2"},{default:p(()=>[_(b(S),1)]),_:2},1032,["onClose"]))),128)),f.inputVisible?(s(),Z(r,{key:0,ref_for:!0,ref:"inputRef",modelValue:f.inputValue,"onUpdate:modelValue":S=>f.inputValue=S,size:"small",style:{width:"100px"},onKeyup:ge(S=>k(H),["enter"]),onBlur:S=>k(H)},null,8,["modelValue","onUpdate:modelValue","onKeyup","onBlur"])):(s(),Z(g,{key:1,size:"small",onClick:S=>O(H)},{default:p(()=>t[23]||(t[23]=[_("+ 添加值")])),_:2,__:[23]},1032,["onClick"]))]),u(g,{type:"danger",onClick:S=>P(H),size:"small",circle:""},{default:p(()=>t[24]||(t[24]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)])),_:2,__:[24]},1032,["onClick"])]))),128))])):W("",!0),T.value.length>0?(s(),n("div",At,[e("table",Dt,[e("thead",Pt,[e("tr",null,[(s(!0),n(L,null,E(o.value.skuAttributes,f=>(s(),n("th",{key:f.name,class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider"},b(f.name),1))),128)),t[25]||(t[25]=e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider"}," 价格 ",-1)),t[26]||(t[26]=e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider"}," 库存 ",-1)),t[27]||(t[27]=e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider"}," SKU图 ",-1))])]),e("tbody",Et,[(s(!0),n(L,null,E(T.value,(f,H)=>(s(),n("tr",{key:H},[(s(!0),n(L,null,E(o.value.skuAttributes,S=>(s(),n("td",{key:S.name,class:"px-4 py-3 text-sm text-gray-900 dark:text-dark-text"},b(f.attributes[S.name]),1))),128)),e("td",Rt,[u(j,{modelValue:f.price,"onUpdate:modelValue":S=>f.price=S,min:0,precision:2,step:.1,size:"small",style:{width:"120px"}},null,8,["modelValue","onUpdate:modelValue"])]),e("td",Ht,[u(j,{modelValue:f.stock,"onUpdate:modelValue":S=>f.stock=S,min:0,precision:0,step:1,size:"small",style:{width:"120px"}},null,8,["modelValue","onUpdate:modelValue"])]),e("td",Tt,[u(ee,{modelValue:f.image,"onUpdate:modelValue":S=>f.image=S,placeholder:"选择SKU图",size:"small",style:{width:"120px"}},{default:p(()=>[(s(!0),n(L,null,E(o.value.skuImages,(S,te)=>(s(),Z(w,{key:te,label:`图片${te+1}`,value:S},{default:p(()=>[e("div",Ft,[e("img",{src:S,class:"w-8 h-8 object-cover rounded mr-2"},null,8,Xt),e("span",null,"图片"+b(te+1),1)])]),_:2},1032,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])])]))),128))])])])):o.value.skuAttributes.length>0?(s(),n("div",Yt," 请为每个属性添加值，系统将自动生成SKU组合 ")):(s(),n("div",Nt," 请添加SKU属性，如颜色、尺码等 "))])]),_:1},8,["model","rules"])]),_:1},8,["modelValue"]),u(ie,{modelValue:z.value,"onUpdate:modelValue":t[8]||(t[8]=f=>z.value=f),title:d.value,multiple:i.value!=="cover",onSelect:R},null,8,["modelValue","title","multiple"]),u(ne,{modelValue:A.value,"onUpdate:modelValue":t[9]||(t[9]=f=>A.value=f),onSuccess:a},null,8,["modelValue"])],64)}}}),Gt=oe(Wt,[["__scopeId","data-v-8a9611a0"]]),Qt={class:"flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border"},Jt={class:"flex items-center space-x-3"},Ot={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Zt={key:0,class:"p-6 space-y-8"},er={class:"grid grid-cols-1 md:grid-cols-2 gap-8"},tr={class:"mb-4"},rr={class:"grid grid-cols-5 gap-2"},or=["src"],sr=["onClick"],lr=["src"],ar={class:"space-y-6"},dr={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},nr={class:"mt-2 flex items-center space-x-4"},ir={class:"text-xl font-bold text-amber-600 dark:text-amber-400"},ur={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},cr={class:"border-t border-gray-100 dark:border-dark-border pt-4"},mr={class:"grid grid-cols-2 gap-4"},pr={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},gr={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},vr={class:"border-t border-gray-100 dark:border-dark-border pt-4"},kr={class:"flex space-x-3"},br={class:"border-t border-gray-100 dark:border-dark-border pt-6"},xr={class:"font-mono text-sm text-gray-600 dark:text-dark-text-secondary"},fr={class:"flex flex-wrap gap-2"},hr={class:"font-medium text-green-600 dark:text-green-400"},yr={class:"font-medium text-gray-900 dark:text-dark-text"},wr={key:0,class:"flex justify-center"},_r={key:1,class:"text-gray-400"},$r=re({__name:"ViewProductDialog",props:{modelValue:{type:Boolean},product:{}},emits:["update:modelValue"],setup(G,{emit:N}){const D=G,B=N,U=Q({get:()=>D.modelValue,set:d=>B("update:modelValue",d)}),$=x(""),C=()=>{U.value=!1},z=d=>{$.value=d},A=()=>{J.success("编辑商品功能开发中...")},i=()=>{J.success("创建刊登功能开发中...")};return(d,o)=>{const V=h("el-image"),T=h("el-button"),M=h("el-table-column"),R=h("el-tag"),q=h("el-table"),y=h("el-dialog");return s(),Z(y,{modelValue:U.value,"onUpdate:modelValue":o[1]||(o[1]=l=>U.value=l),width:"1200px","before-close":C,"show-close":!1,class:"modern-dialog"},{header:p(()=>{var l;return[e("div",Qt,[e("div",Jt,[o[3]||(o[3]=e("div",{class:"w-10 h-10 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})])],-1)),e("div",null,[o[2]||(o[2]=e("h3",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"商品详情",-1)),e("p",Ot,"ID: "+b(((l=d.product)==null?void 0:l.id)||""),1)])]),e("button",{onClick:C,class:"p-2 text-gray-400 hover:text-gray-600 dark:text-dark-text-secondary dark:hover:text-dark-text rounded-lg hover:bg-gray-100 dark:hover:bg-dark-card transition-all duration-200"},o[4]||(o[4]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]}),footer:p(()=>[e("div",{class:"flex items-center justify-end p-6 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},[e("button",{onClick:C,class:"px-6 py-2.5 text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200"}," 关闭 ")])]),default:p(()=>[d.product?(s(),n("div",Zt,[e("div",er,[e("div",null,[e("div",tr,[u(V,{src:d.product.coverImage,"preview-src-list":[d.product.coverImage,...d.product.additionalImages],fit:"cover",class:"w-full h-80 object-cover rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"])]),e("div",rr,[e("div",{class:"cursor-pointer border-2 rounded-lg overflow-hidden transition-all duration-200 border-amber-500",onClick:o[0]||(o[0]=l=>z(d.product.coverImage))},[e("img",{src:d.product.coverImage,class:"w-full h-16 object-cover"},null,8,or)]),(s(!0),n(L,null,E(d.product.additionalImages,(l,F)=>(s(),n("div",{key:F,class:"cursor-pointer border-2 border-gray-200 dark:border-dark-border hover:border-amber-500 rounded-lg overflow-hidden transition-all duration-200",onClick:P=>z(l)},[e("img",{src:l,class:"w-full h-16 object-cover"},null,8,lr)],8,sr))),128))])]),e("div",ar,[e("div",null,[e("h2",dr,b(d.product.name),1),e("div",nr,[e("div",ir," ¥"+b(d.product.minPrice.toFixed(2)),1),e("div",ur," SKU数量: "+b(d.product.skuCount),1)])]),e("div",cr,[o[7]||(o[7]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-3"},"创建信息",-1)),e("div",mr,[e("div",null,[o[5]||(o[5]=e("p",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"创建人",-1)),e("p",pr,b(d.product.creator),1)]),e("div",null,[o[6]||(o[6]=e("p",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"创建时间",-1)),e("p",gr,b(d.product.createTime),1)])])]),e("div",vr,[o[10]||(o[10]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-3"},"操作",-1)),e("div",kr,[u(T,{type:"primary",onClick:A},{default:p(()=>o[8]||(o[8]=[e("svg",{class:"w-5 h-5 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),_(" 编辑商品 ")])),_:1,__:[8]}),u(T,{onClick:i},{default:p(()=>o[9]||(o[9]=[e("svg",{class:"w-5 h-5 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})],-1),_(" 创建刊登 ")])),_:1,__:[9]})])])])]),e("div",br,[o[11]||(o[11]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-4"},"SKU信息",-1)),u(q,{data:d.product.skus,style:{width:"100%"},class:"modern-table","header-cell-style":{backgroundColor:"var(--el-bg-color-page)",color:"var(--el-text-color-primary)",fontWeight:"600",borderBottom:"1px solid var(--el-border-color-light)"},"row-style":{backgroundColor:"transparent"}},{default:p(()=>[u(M,{prop:"id",label:"SKU ID",width:"120"},{default:p(l=>[e("span",xr,b(l.row.id),1)]),_:1}),u(M,{label:"属性","min-width":"200"},{default:p(l=>[e("div",fr,[(s(!0),n(L,null,E(l.row.attributes,(F,P)=>(s(),Z(R,{key:P,size:"small",class:"mr-1"},{default:p(()=>[_(b(P)+": "+b(F),1)]),_:2},1024))),128))])]),_:1}),u(M,{prop:"price",label:"价格",width:"120"},{default:p(l=>[e("span",hr,"¥"+b(l.row.price.toFixed(2)),1)]),_:1}),u(M,{prop:"stock",label:"库存",width:"100"},{default:p(l=>[e("span",yr,b(l.row.stock),1)]),_:1}),u(M,{label:"SKU图",width:"100"},{default:p(l=>[l.row.image?(s(),n("div",wr,[u(V,{src:l.row.image,"preview-src-list":[l.row.image],fit:"cover",class:"w-12 h-12 rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"])])):(s(),n("span",_r,"-"))]),_:1})]),_:1},8,["data"])])])):W("",!0)]),_:1},8,["modelValue"])}}}),Vr=oe($r,[["__scopeId","data-v-166d4f86"]]),Cr={key:0,class:"space-y-6"},Ir={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-6"},jr={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Ur={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-6"},zr={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},Sr={key:0,class:"relative"},Mr=["src"],Lr={key:1,class:"h-40 flex flex-col items-center justify-center"},Kr={key:0,class:"relative"},Br={class:"grid grid-cols-2 gap-2 h-40"},Ar=["src","alt"],Dr=["onClick"],Pr={key:0,class:"absolute bottom-2 right-2 bg-gray-800 bg-opacity-70 text-white text-xs px-2 py-1 rounded-full"},Er={key:1,class:"h-40 flex flex-col items-center justify-center"},Rr={key:0,class:"relative"},Hr={class:"grid grid-cols-2 gap-2 h-40"},Tr=["src","alt"],Fr=["onClick"],Xr={key:0,class:"absolute bottom-2 right-2 bg-gray-800 bg-opacity-70 text-white text-xs px-2 py-1 rounded-full"},Yr={key:1,class:"h-40 flex flex-col items-center justify-center"},Nr={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-6"},qr={class:"border border-gray-200 dark:border-dark-border rounded-lg overflow-hidden"},Wr={class:"min-w-full divide-y divide-gray-200 dark:divide-dark-border"},Gr={class:"bg-white dark:bg-dark-surface divide-y divide-gray-200 dark:divide-dark-border"},Qr={class:"px-4 py-3 text-sm text-gray-900 dark:text-dark-text"},Jr={class:"px-4 py-3 text-sm"},Or={class:"flex flex-wrap gap-1"},Zr={class:"px-4 py-3 text-sm"},eo={class:"px-4 py-3 text-sm"},to={class:"px-4 py-3 text-sm"},ro={class:"flex items-center"},oo=["src"],so={class:"flex justify-end space-x-3"},lo=re({__name:"EditProductDialog",props:{modelValue:{type:Boolean},product:{}},emits:["update:modelValue","success"],setup(G,{emit:N}){const D=G,B=N,U=Q({get:()=>D.modelValue,set:y=>B("update:modelValue",y)}),$=x(!1),C=x(!1),z=x("cover"),A=Q(()=>({cover:"选择封面图",additional:"选择附图",sku:"选择SKU图"})[z.value]),i=x({name:"",price:0,coverImage:"",additionalImages:[],skuImages:[],skus:[]});ke(()=>D.product,y=>{y&&(i.value={name:y.name,price:y.minPrice,coverImage:y.coverImage,additionalImages:[...y.additionalImages],skuImages:[],skus:[...y.skus]})},{immediate:!0});const d=y=>{z.value=y,C.value=!0},o=y=>{z.value==="cover"?i.value.coverImage=y[0]:z.value==="additional"?i.value.additionalImages=[...i.value.additionalImages,...y]:z.value==="sku"&&(i.value.skuImages=[...i.value.skuImages,...y])},V=y=>{i.value.coverImage=""},T=y=>{i.value.additionalImages.splice(y,1)},M=y=>{i.value.skuImages.splice(y,1)},R=()=>{$.value=!0,setTimeout(()=>{$.value=!1,J.success("商品修改成功！"),B("success"),B("update:modelValue",!1)},1e3)},q=()=>{};return(y,l)=>{const F=h("el-input"),P=h("el-input-number"),O=h("el-tag"),k=h("el-option"),c=h("el-select"),K=h("el-button"),I=h("el-dialog");return s(),n(L,null,[u(I,{modelValue:U.value,"onUpdate:modelValue":l[7]||(l[7]=a=>U.value=a),title:"编辑商品",width:"1000px","align-center":"",onClose:q,class:"edit-product-dialog"},{footer:p(()=>[e("div",so,[u(K,{onClick:l[6]||(l[6]=a=>U.value=!1)},{default:p(()=>l[24]||(l[24]=[_("取消")])),_:1,__:[24]}),u(K,{type:"primary",onClick:R,loading:$.value},{default:p(()=>l[25]||(l[25]=[_("保存修改")])),_:1,__:[25]},8,["loading"])])]),default:p(()=>[y.product?(s(),n("div",Cr,[e("div",Ir,[l[11]||(l[11]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-4"},"基本信息",-1)),e("div",jr,[e("div",null,[l[9]||(l[9]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},"商品标题",-1)),u(F,{modelValue:i.value.name,"onUpdate:modelValue":l[0]||(l[0]=a=>i.value.name=a),placeholder:"请输入商品标题",maxlength:"100","show-word-limit":""},null,8,["modelValue"])]),e("div",null,[l[10]||(l[10]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},"商品价格",-1)),u(P,{modelValue:i.value.price,"onUpdate:modelValue":l[1]||(l[1]=a=>i.value.price=a),min:0,precision:2,step:.1,style:{width:"100%"}},null,8,["modelValue"])])])]),e("div",Ur,[l[21]||(l[21]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-4"},"商品图片",-1)),e("div",zr,[e("div",null,[l[14]||(l[14]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},"封面图",-1)),e("div",{class:"border-2 border-dashed border-gray-300 dark:border-dark-border rounded-lg p-4 text-center cursor-pointer hover:border-amber-500 dark:hover:border-amber-500 transition-colors",onClick:l[3]||(l[3]=a=>d("cover"))},[i.value.coverImage?(s(),n("div",Sr,[e("img",{src:i.value.coverImage,alt:"封面图",class:"w-full h-40 object-cover rounded-lg"},null,8,Mr),e("button",{onClick:l[2]||(l[2]=Y(a=>V("cover"),["stop"])),class:"absolute top-2 right-2 w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600"},l[12]||(l[12]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])):(s(),n("div",Lr,l[13]||(l[13]=[e("svg",{class:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"mt-2 text-sm text-gray-500 dark:text-dark-text-secondary"},"点击更换封面图",-1)])))])]),e("div",null,[l[17]||(l[17]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},"附图（多张）",-1)),e("div",{class:"border-2 border-dashed border-gray-300 dark:border-dark-border rounded-lg p-4 text-center cursor-pointer hover:border-amber-500 dark:hover:border-amber-500 transition-colors",onClick:l[4]||(l[4]=a=>d("additional"))},[i.value.additionalImages.length>0?(s(),n("div",Kr,[e("div",Br,[(s(!0),n(L,null,E(i.value.additionalImages.slice(0,4),(a,m)=>(s(),n("div",{key:m,class:"relative"},[e("img",{src:a,alt:`附图${m+1}`,class:"w-full h-full object-cover rounded-lg"},null,8,Ar),e("button",{onClick:Y(t=>T(m),["stop"]),class:"absolute top-1 right-1 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600"},l[15]||(l[15]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,Dr)]))),128))]),i.value.additionalImages.length>4?(s(),n("div",Pr," +"+b(i.value.additionalImages.length-4),1)):W("",!0)])):(s(),n("div",Er,l[16]||(l[16]=[e("svg",{class:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"mt-2 text-sm text-gray-500 dark:text-dark-text-secondary"},"点击添加附图",-1)])))])]),e("div",null,[l[20]||(l[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},"SKU图",-1)),e("div",{class:"border-2 border-dashed border-gray-300 dark:border-dark-border rounded-lg p-4 text-center cursor-pointer hover:border-amber-500 dark:hover:border-amber-500 transition-colors",onClick:l[5]||(l[5]=a=>d("sku"))},[i.value.skuImages.length>0?(s(),n("div",Rr,[e("div",Hr,[(s(!0),n(L,null,E(i.value.skuImages.slice(0,4),(a,m)=>(s(),n("div",{key:m,class:"relative"},[e("img",{src:a,alt:`SKU图${m+1}`,class:"w-full h-full object-cover rounded-lg"},null,8,Tr),e("button",{onClick:Y(t=>M(m),["stop"]),class:"absolute top-1 right-1 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600"},l[18]||(l[18]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,Fr)]))),128))]),i.value.skuImages.length>4?(s(),n("div",Xr," +"+b(i.value.skuImages.length-4),1)):W("",!0)])):(s(),n("div",Yr,l[19]||(l[19]=[e("svg",{class:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"mt-2 text-sm text-gray-500 dark:text-dark-text-secondary"},"点击添加SKU图",-1)])))])])])]),e("div",Nr,[l[23]||(l[23]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-4"},"SKU信息",-1)),e("div",qr,[e("table",Wr,[l[22]||(l[22]=e("thead",{class:"bg-gray-100 dark:bg-dark-border"},[e("tr",null,[e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider"}," SKU ID "),e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider"}," 属性 "),e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider"}," 价格 "),e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider"}," 库存 "),e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-secondary uppercase tracking-wider"}," SKU图 ")])],-1)),e("tbody",Gr,[(s(!0),n(L,null,E(i.value.skus,(a,m)=>(s(),n("tr",{key:m},[e("td",Qr,b(a.id),1),e("td",Jr,[e("div",Or,[(s(!0),n(L,null,E(a.attributes,(t,r)=>(s(),Z(O,{key:r,size:"small"},{default:p(()=>[_(b(r)+": "+b(t),1)]),_:2},1024))),128))])]),e("td",Zr,[u(P,{modelValue:a.price,"onUpdate:modelValue":t=>a.price=t,min:0,precision:2,step:.1,size:"small",style:{width:"120px"}},null,8,["modelValue","onUpdate:modelValue"])]),e("td",eo,[u(P,{modelValue:a.stock,"onUpdate:modelValue":t=>a.stock=t,min:0,precision:0,step:1,size:"small",style:{width:"120px"}},null,8,["modelValue","onUpdate:modelValue"])]),e("td",to,[u(c,{modelValue:a.image,"onUpdate:modelValue":t=>a.image=t,placeholder:"选择SKU图",size:"small",style:{width:"120px"}},{default:p(()=>[(s(!0),n(L,null,E(i.value.skuImages,(t,r)=>(s(),Z(k,{key:r,label:`图片${r+1}`,value:t},{default:p(()=>[e("div",ro,[e("img",{src:t,class:"w-8 h-8 object-cover rounded mr-2"},null,8,oo),e("span",null,"图片"+b(r+1),1)])]),_:2},1032,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])])]))),128))])])])])])):W("",!0)]),_:1},8,["modelValue"]),u(ie,{modelValue:C.value,"onUpdate:modelValue":l[8]||(l[8]=a=>C.value=a),title:A.value,multiple:z.value!=="cover",onSelect:o},null,8,["modelValue","title","multiple"])],64)}}}),ao=oe(lo,[["__scopeId","data-v-7d49c9bd"]]),no={class:"space-y-6"},io={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},uo={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},co={class:"flex items-center justify-between"},mo={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},po={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},go={class:"flex items-center justify-between"},vo={class:"text-2xl font-bold text-green-600 dark:text-green-400"},ko={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},bo={class:"flex items-center justify-between"},xo={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},fo={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},ho={class:"flex items-center justify-between"},yo={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},wo={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},_o={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},$o={class:"flex items-center space-x-3"},Vo={key:0,class:"flex items-center space-x-2 ml-4 pl-4 border-l border-gray-200 dark:border-dark-border"},Co={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Io={class:"relative"},jo={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},Uo={class:"overflow-x-auto"},zo={class:"flex justify-center"},So={class:"font-medium text-gray-900 dark:text-dark-text"},Mo={class:"font-medium text-green-600 dark:text-green-400"},Lo={class:"text-sm font-medium text-blue-600 dark:text-blue-400"},Ko={class:"flex items-center space-x-2"},Bo={class:"w-6 h-6 bg-gradient-to-br from-amber-400 to-amber-500 rounded-full flex items-center justify-center"},Ao={class:"text-white text-xs font-medium"},Do={class:"text-sm text-gray-900 dark:text-dark-text"},Po={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Eo={class:"flex items-center space-x-2"},Ro=["onClick"],Ho=["onClick"],To={class:"flex justify-between items-center px-6 py-4 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},Fo={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Xo=re({__name:"index",setup(G){const N=x(!1),D=x(!1),B=x(!1),U=x(!1),$=x(null),C=x([]),z=x(""),A=x(128),i=x(456),d=x(12),o=x(199.99),V=x({currentPage:1,pageSize:10,total:0}),T=x([{id:"P001",name:"时尚休闲T恤 纯棉圆领短袖",minPrice:69.9,skuCount:6,coverImage:"https://picsum.photos/400/400?random=1",additionalImages:["https://picsum.photos/400/400?random=11","https://picsum.photos/400/400?random=12"],skus:[{id:"SKU001",attributes:{color:"白色",size:"M"},price:69.9,stock:100},{id:"SKU002",attributes:{color:"白色",size:"L"},price:69.9,stock:80},{id:"SKU003",attributes:{color:"黑色",size:"M"},price:69.9,stock:90},{id:"SKU004",attributes:{color:"黑色",size:"L"},price:69.9,stock:70},{id:"SKU005",attributes:{color:"蓝色",size:"M"},price:69.9,stock:85},{id:"SKU006",attributes:{color:"蓝色",size:"L"},price:69.9,stock:65}],creator:"张三",createTime:"2024-01-15 14:30:25"},{id:"P002",name:"夏季薄款牛仔裤 直筒宽松",minPrice:129.9,skuCount:4,coverImage:"https://picsum.photos/400/400?random=2",additionalImages:["https://picsum.photos/400/400?random=21","https://picsum.photos/400/400?random=22"],skus:[{id:"SKU007",attributes:{color:"浅蓝",size:"30"},price:129.9,stock:50},{id:"SKU008",attributes:{color:"浅蓝",size:"32"},price:129.9,stock:45},{id:"SKU009",attributes:{color:"深蓝",size:"30"},price:129.9,stock:55},{id:"SKU010",attributes:{color:"深蓝",size:"32"},price:129.9,stock:40}],creator:"李四",createTime:"2024-01-15 13:45:12"},{id:"P003",name:"运动休闲鞋 轻便透气",minPrice:199.9,skuCount:8,coverImage:"https://picsum.photos/400/400?random=3",additionalImages:["https://picsum.photos/400/400?random=31","https://picsum.photos/400/400?random=32"],skus:[{id:"SKU011",attributes:{color:"白色",size:"39"},price:199.9,stock:30},{id:"SKU012",attributes:{color:"白色",size:"40"},price:199.9,stock:35},{id:"SKU013",attributes:{color:"白色",size:"41"},price:199.9,stock:40},{id:"SKU014",attributes:{color:"白色",size:"42"},price:199.9,stock:38},{id:"SKU015",attributes:{color:"黑色",size:"39"},price:199.9,stock:32},{id:"SKU016",attributes:{color:"黑色",size:"40"},price:199.9,stock:36},{id:"SKU017",attributes:{color:"黑色",size:"41"},price:199.9,stock:42},{id:"SKU018",attributes:{color:"黑色",size:"42"},price:199.9,stock:40}],creator:"王五",createTime:"2024-01-15 12:20:08"},{id:"P004",name:"女士连衣裙 碎花雪纺",minPrice:159.9,skuCount:6,coverImage:"https://picsum.photos/400/400?random=4",additionalImages:["https://picsum.photos/400/400?random=41","https://picsum.photos/400/400?random=42"],skus:[{id:"SKU019",attributes:{color:"蓝色",size:"S"},price:159.9,stock:25},{id:"SKU020",attributes:{color:"蓝色",size:"M"},price:159.9,stock:30},{id:"SKU021",attributes:{color:"蓝色",size:"L"},price:159.9,stock:20},{id:"SKU022",attributes:{color:"粉色",size:"S"},price:159.9,stock:28},{id:"SKU023",attributes:{color:"粉色",size:"M"},price:159.9,stock:32},{id:"SKU024",attributes:{color:"粉色",size:"L"},price:159.9,stock:22}],creator:"赵六",createTime:"2024-01-15 11:15:33"},{id:"P005",name:"男士商务西装 修身款",minPrice:599.9,skuCount:9,coverImage:"https://picsum.photos/400/400?random=5",additionalImages:["https://picsum.photos/400/400?random=51","https://picsum.photos/400/400?random=52"],skus:[{id:"SKU025",attributes:{color:"黑色",size:"48"},price:599.9,stock:15},{id:"SKU026",attributes:{color:"黑色",size:"50"},price:599.9,stock:18},{id:"SKU027",attributes:{color:"黑色",size:"52"},price:599.9,stock:12},{id:"SKU028",attributes:{color:"藏青",size:"48"},price:599.9,stock:16},{id:"SKU029",attributes:{color:"藏青",size:"50"},price:599.9,stock:19},{id:"SKU030",attributes:{color:"藏青",size:"52"},price:599.9,stock:14},{id:"SKU031",attributes:{color:"灰色",size:"48"},price:599.9,stock:17},{id:"SKU032",attributes:{color:"灰色",size:"50"},price:599.9,stock:20},{id:"SKU033",attributes:{color:"灰色",size:"52"},price:599.9,stock:15}],creator:"钱七",createTime:"2024-01-15 10:30:45"}]),M=Q(()=>{let I=T.value;z.value&&(I=I.filter(t=>t.name.toLowerCase().includes(z.value.toLowerCase())||t.skus.some(r=>r.id.toLowerCase().includes(z.value.toLowerCase()))));const a=(V.value.currentPage-1)*V.value.pageSize,m=a+V.value.pageSize;return I.slice(a,m)});de(()=>{R()});const R=()=>{N.value=!0,setTimeout(()=>{V.value.total=T.value.length,N.value=!1},500)},q=I=>{C.value=I},y=()=>{V.value.currentPage=1,R()},l=I=>{$.value=I,B.value=!0},F=I=>{$.value=I,U.value=!0},P=()=>{J.success("导出商品功能开发中...")},O=()=>{J.success(`正在批量导出 ${C.value.length} 个商品...`)},k=()=>{J.success("操作成功！"),R()},c=I=>{V.value.pageSize=I,V.value.currentPage=1,R()},K=I=>{V.value.currentPage=I,R()};return(I,a)=>{const m=h("el-table-column"),t=h("el-image"),r=h("el-table"),v=h("el-pagination"),j=fe("loading");return s(),n(L,null,[e("div",no,[a[20]||(a[20]=be('<div class="bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20 rounded-2xl p-6 border border-amber-100 dark:border-amber-800" data-v-5c32293d><div class="flex items-center space-x-3" data-v-5c32293d><div class="w-10 h-10 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center" data-v-5c32293d><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-5c32293d><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" data-v-5c32293d></path></svg></div><div data-v-5c32293d><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-5c32293d>白品管理</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-5c32293d>管理您的基础商品信息和SKU</p></div></div></div>',1)),e("div",io,[e("div",uo,[e("div",co,[e("div",null,[a[7]||(a[7]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"商品总数",-1)),e("p",mo,b(A.value),1)]),a[8]||(a[8]=e("div",{class:"w-12 h-12 bg-amber-100 dark:bg-amber-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-amber-600 dark:text-amber-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})])],-1))])]),e("div",po,[e("div",go,[e("div",null,[a[9]||(a[9]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"SKU总数",-1)),e("p",vo,b(i.value),1)]),a[10]||(a[10]=e("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})])],-1))])]),e("div",ko,[e("div",bo,[e("div",null,[a[11]||(a[11]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"今日新增",-1)),e("p",xo,b(d.value),1)]),a[12]||(a[12]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1))])]),e("div",fo,[e("div",ho,[e("div",null,[a[13]||(a[13]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"平均价格",-1)),e("p",yo,"¥"+b(o.value),1)]),a[14]||(a[14]=e("div",{class:"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])])]),e("div",wo,[e("div",_o,[e("div",$o,[e("button",{onClick:a[0]||(a[0]=g=>D.value=!0),class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},a[15]||(a[15]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),_(" 创建商品 ")])),e("button",{onClick:P,class:"inline-flex items-center px-4 py-2.5 bg-white dark:bg-dark-card text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border shadow-sm hover:shadow-md transition-all duration-200"},a[16]||(a[16]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})],-1),_(" 导出商品 ")])),C.value.length>0?(s(),n("div",Vo,[e("span",Co," 已选择 "+b(C.value.length)+" 项 ",1),e("button",{onClick:O,class:"inline-flex items-center px-3 py-1.5 bg-amber-500 hover:bg-amber-600 text-white text-sm font-medium rounded-lg transition-all duration-200"},a[17]||(a[17]=[e("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})],-1),_(" 批量导出 ")]))])):W("",!0)]),e("div",Io,[ae(e("input",{"onUpdate:modelValue":a[1]||(a[1]=g=>z.value=g),type:"text",placeholder:"搜索商品名称、SKU...",class:"w-full sm:w-64 pl-10 pr-4 py-2 border border-gray-300 dark:border-dark-border rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500 dark:bg-dark-card dark:text-dark-text",onInput:y},null,544),[[xe,z.value]]),a[18]||(a[18]=e("svg",{class:"w-5 h-5 text-gray-400 absolute left-3 top-2.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1))])])]),e("div",jo,[a[19]||(a[19]=e("div",{class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"商品列表"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},"管理您的所有商品信息")],-1)),e("div",Uo,[ae((s(),Z(r,{data:M.value,style:{width:"100%"},onSelectionChange:q,class:"modern-table","header-cell-style":{backgroundColor:"var(--el-bg-color-page)",color:"var(--el-text-color-primary)",fontWeight:"600",borderBottom:"1px solid var(--el-border-color-light)"},"row-style":{backgroundColor:"transparent"}},{default:p(()=>[u(m,{type:"selection",width:"55"}),u(m,{label:"商品封面图",width:"100"},{default:p(g=>[e("div",zo,[u(t,{src:g.row.coverImage,"preview-src-list":[g.row.coverImage],fit:"cover",class:"w-16 h-16 rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"])])]),_:1}),u(m,{prop:"name",label:"商品名称","min-width":"200"},{default:p(g=>[e("div",So,b(g.row.name),1)]),_:1}),u(m,{prop:"price",label:"价格（最低）",width:"120"},{default:p(g=>[e("span",Mo," ¥"+b(g.row.minPrice),1)]),_:1}),u(m,{prop:"skuCount",label:"SKU数量",width:"100"},{default:p(g=>[e("span",Lo,b(g.row.skuCount),1)]),_:1}),u(m,{prop:"creator",label:"创建人",width:"100"},{default:p(g=>[e("div",Ko,[e("div",Bo,[e("span",Ao,b(g.row.creator.charAt(0)),1)]),e("span",Do,b(g.row.creator),1)])]),_:1}),u(m,{prop:"createTime",label:"创建时间",width:"180"},{default:p(g=>[e("div",Po,b(g.row.createTime),1)]),_:1}),u(m,{label:"操作",width:"180"},{default:p(g=>[e("div",Eo,[e("button",{onClick:X=>l(g.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-amber-600 dark:text-amber-400 hover:text-amber-700 dark:hover:text-amber-300 bg-amber-50 dark:bg-amber-900/20 hover:bg-amber-100 dark:hover:bg-amber-900/30 rounded-lg transition-all duration-200"}," 查看详情 ",8,Ro),e("button",{onClick:X=>F(g.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-card hover:bg-gray-100 dark:hover:bg-dark-border rounded-lg transition-all duration-200"}," 编辑 ",8,Ho)])]),_:1})]),_:1},8,["data"])),[[j,N.value]])]),e("div",To,[e("div",Fo," 共 "+b(V.value.total)+" 条记录 ",1),u(v,{"current-page":V.value.currentPage,"onUpdate:currentPage":a[2]||(a[2]=g=>V.value.currentPage=g),"page-size":V.value.pageSize,"onUpdate:pageSize":a[3]||(a[3]=g=>V.value.pageSize=g),"page-sizes":[10,20,50,100],total:V.value.total,layout:"sizes, prev, pager, next, jumper",onSizeChange:c,onCurrentChange:K,class:"modern-pagination"},null,8,["current-page","page-size","total"])])])]),u(Gt,{modelValue:D.value,"onUpdate:modelValue":a[4]||(a[4]=g=>D.value=g),onSuccess:k},null,8,["modelValue"]),u(Vr,{modelValue:B.value,"onUpdate:modelValue":a[5]||(a[5]=g=>B.value=g),product:$.value},null,8,["modelValue","product"]),u(ao,{modelValue:U.value,"onUpdate:modelValue":a[6]||(a[6]=g=>U.value=g),product:$.value,onSuccess:k},null,8,["modelValue","product"])],64)}}}),qo=oe(Xo,[["__scopeId","data-v-5c32293d"]]);export{qo as default};
