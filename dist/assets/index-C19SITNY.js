import{c as l,a as e,o as t,d as K,e as $,n as j,q as S,B as C,h as M,j as k,E as A,t as n,_ as Z,w as Q,g,C as I,F as N,k as T,r as h,M as Ce,p as pe,b as J,N as Ae,H as ee,x as Ve,l as Ie,f as Se,v as Me,W as re,s as Ne}from"./index-ByC_LaEp.js";import{r as ve}from"./CheckCircleIcon-DQ6MhbFo.js";import{r as xe}from"./InformationCircleIcon-DqPQfm_b.js";import{r as Te}from"./CheckIcon-BcKHPbyn.js";import{r as De}from"./MagnifyingGlassIcon-Dye5Mj9n.js";function Oe(o,u){return t(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3"})])}function je(o,u){return t(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18"})])}function ze(o,u){return t(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])}function Ue(o,u){return t(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"})])}function ce(o,u){return t(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12Z"})])}function oe(o,u){return t(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"})])}const Fe={key:0,class:"flex items-center space-x-2"},Ee={key:1,class:"space-y-1"},Pe={class:"flex items-baseline space-x-2"},Be={class:"price-text text-primary-600 dark:text-primary-400 font-bold"},Le={key:0,class:"original-price text-gray-400 dark:text-gray-500 line-through text-sm"},Re={key:2,class:"space-y-1"},Ye={class:"flex items-baseline space-x-2"},Ge={class:"price-text text-purple-600 dark:text-purple-400 font-bold"},He={class:"price-unit text-gray-500 dark:text-dark-text-secondary"},Je={key:0,class:"original-price text-gray-400 dark:text-gray-500 line-through text-sm"},qe={class:"price-desc text-gray-500 dark:text-dark-text-secondary"},Ke={key:3,class:"space-y-1"},Ze={class:"flex items-baseline space-x-2"},We={class:"price-text text-orange-600 dark:text-orange-400 font-bold"},Qe={class:"price-unit text-gray-500 dark:text-dark-text-secondary"},Xe={class:"price-desc text-gray-500 dark:text-dark-text-secondary"},et={key:4,class:"space-y-1"},tt=K({__name:"PriceDisplay",props:{price:{},size:{},showDescription:{type:Boolean}},setup(o){const u=o,i=$(()=>{switch(u.size||"medium"){case"small":return"price-small";case"large":return"price-large";default:return"price-medium"}});return(s,a)=>{const c=M("el-tag");return t(),l("div",{class:j(["price-display",i.value])},[s.price.type==="free"?(t(),l("div",Fe,[a[1]||(a[1]=e("span",{class:"price-text text-green-600 dark:text-green-400 font-bold"}," 免费 ",-1)),s.size!=="small"?(t(),S(c,{key:0,type:"success",size:"small",effect:"plain"},{default:k(()=>a[0]||(a[0]=[A(" FREE ")])),_:1,__:[0]})):C("",!0)])):s.price.type==="one_time"?(t(),l("div",Ee,[e("div",Pe,[e("span",Be," ¥"+n(s.price.amount),1),s.price.originalAmount&&s.price.originalAmount>s.price.amount?(t(),l("span",Le," ¥"+n(s.price.originalAmount),1)):C("",!0)]),a[2]||(a[2]=e("div",{class:"price-desc text-gray-500 dark:text-dark-text-secondary"}," 一次购买，终身使用 ",-1)),s.price.originalAmount&&s.price.originalAmount>s.price.amount&&s.size!=="small"?(t(),S(c,{key:0,type:"danger",size:"small",effect:"plain",class:"discount-tag"},{default:k(()=>[A(n(Math.round((1-s.price.amount/s.price.originalAmount)*100))+"% OFF ",1)]),_:1})):C("",!0)])):s.price.type==="monthly"?(t(),l("div",Re,[e("div",Ye,[e("span",Ge," ¥"+n(s.price.amount),1),e("span",He,n(s.price.unit||"/月"),1),s.price.originalAmount&&s.price.originalAmount>s.price.amount?(t(),l("span",Je," ¥"+n(s.price.originalAmount),1)):C("",!0)]),e("div",qe,n(s.price.description||"按月订阅，随时取消"),1),s.price.originalAmount&&s.price.originalAmount>s.price.amount&&s.size!=="small"?(t(),S(c,{key:0,type:"warning",size:"small",effect:"plain",class:"discount-tag"},{default:k(()=>a[3]||(a[3]=[A(" 限时优惠 ")])),_:1,__:[3]})):C("",!0)])):s.price.type==="per_use"?(t(),l("div",Ke,[e("div",Ze,[e("span",We," ¥"+n(s.price.amount),1),e("span",Qe,n(s.price.unit||"/次"),1)]),e("div",Xe,n(s.price.description||"按使用次数计费"),1),s.size!=="small"?(t(),S(c,{key:0,type:"info",size:"small",effect:"plain"},{default:k(()=>a[4]||(a[4]=[A(" 按需付费 ")])),_:1,__:[4]})):C("",!0)])):(t(),l("div",et,a[5]||(a[5]=[e("span",{class:"price-text text-gray-600 dark:text-gray-400"}," 价格待定 ",-1)])))],2)}}}),me=Z(tt,[["__scopeId","data-v-470f89ed"]]),at={class:"relative p-6"},st={class:"flex justify-center mb-4"},rt={class:"w-20 h-20 bg-gradient-to-br from-primary-500 to-blue-500 rounded-2xl flex items-center justify-center text-3xl font-bold text-white shadow-lg transform group-hover:scale-105 transition-transform duration-300"},ot={class:"text-center mb-4"},nt={class:"flex items-center justify-center space-x-2 mb-2"},lt={class:"text-xl font-bold text-gray-900 dark:text-dark-text"},it={class:"text-sm text-gray-500 dark:text-dark-text-secondary mb-3"},dt={class:"flex items-center justify-center space-x-2 mb-3"},pt={class:"flex items-center"},ct={class:"text-sm font-medium text-gray-700 dark:text-dark-text"},ut={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},mt={class:"mb-4"},gt={class:"text-sm text-gray-600 dark:text-dark-text-secondary line-clamp-2 leading-relaxed text-center"},vt={class:"mb-6"},yt={class:"flex flex-wrap justify-center gap-2"},ft={class:"px-6 pb-6"},kt={class:"text-center mb-4"},ht={class:"flex flex-col space-y-2"},xt={class:"flex space-x-2"},bt=K({__name:"AppCard",props:{app:{}},emits:["click","favorite-toggle","install-toggle","purchase"],setup(o){const u=o,i=()=>{switch(u.app.price.type){case"free":return"安装";case"one_time":return"购买";case"monthly":return"订阅";case"per_use":return"充值";default:return"安装"}},s=a=>a>=1e4?`${(a/1e4).toFixed(1)}万`:a>=1e3?`${(a/1e3).toFixed(1)}k`:a.toString();return(a,c)=>{const V=M("el-tag");return t(),l("div",{class:"app-card group relative bg-white dark:bg-dark-surface rounded-2xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-xl dark:hover:shadow-2xl transition-all duration-300 cursor-pointer overflow-hidden transform hover:-translate-y-1",onClick:c[5]||(c[5]=f=>a.$emit("click",a.app))},[c[9]||(c[9]=e("div",{class:"absolute top-0 left-0 right-0 h-24 bg-gradient-to-br from-primary-500/10 to-blue-500/10 dark:from-primary-400/20 dark:to-blue-400/20"},null,-1)),e("button",{onClick:c[0]||(c[0]=Q(f=>a.$emit("favorite-toggle",a.app.id),["stop"])),class:j(["absolute top-4 right-4 z-10 p-2 rounded-full bg-white/90 dark:bg-dark-surface/90 backdrop-blur-md shadow-lg border border-white/20 dark:border-dark-border/50 hover:bg-white dark:hover:bg-dark-surface transition-all duration-200 group/fav",a.app.isFavorited?"text-red-500 hover:text-red-600":"text-gray-400 hover:text-red-500"])},[g(I(ce),{class:j(["w-4 h-4 transition-transform duration-200 group-hover/fav:scale-110",a.app.isFavorited?"fill-current":""])},null,8,["class"])],2),e("div",at,[e("div",st,[e("div",rt,n(a.app.icon),1)]),e("div",ot,[e("div",nt,[e("h3",lt,n(a.app.name),1),a.app.status==="maintenance"?(t(),S(V,{key:0,type:"warning",size:"small",effect:"light",round:""},{default:k(()=>c[6]||(c[6]=[A(" 维护中 ")])),_:1,__:[6]})):a.app.status==="deprecated"?(t(),S(V,{key:1,type:"danger",size:"small",effect:"light",round:""},{default:k(()=>c[7]||(c[7]=[A(" 已废弃 ")])),_:1,__:[7]})):C("",!0)]),e("p",it,n(a.app.developer),1),e("div",dt,[e("div",pt,[(t(),l(N,null,T(5,f=>g(I(oe),{key:f,class:j(["w-4 h-4",f<=Math.floor(a.app.rating)?"text-yellow-400 fill-current":"text-gray-300 dark:text-gray-600"])},null,8,["class"])),64))]),e("span",ct,n(a.app.rating),1),e("span",ut," ("+n(s(a.app.downloadCount))+") ",1)])]),e("div",mt,[e("p",gt,n(a.app.description),1)]),e("div",vt,[e("div",yt,[(t(!0),l(N,null,T(a.app.tags.slice(0,3),f=>(t(),S(V,{key:f,size:"small",effect:"light",round:"",class:"text-xs"},{default:k(()=>[A(n(f),1)]),_:2},1024))),128)),a.app.tags.length>3?(t(),S(V,{key:0,size:"small",effect:"plain",round:"",class:"text-xs"},{default:k(()=>[A(" +"+n(a.app.tags.length-3),1)]),_:1})):C("",!0)])])]),e("div",ft,[e("div",kt,[g(me,{price:a.app.price,size:"medium"},null,8,["price"])]),e("div",ht,[a.app.isInstalled?(t(),l("button",{key:1,onClick:c[2]||(c[2]=Q(f=>a.$emit("install-toggle",a.app.id),["stop"])),class:"w-full py-3 px-4 bg-green-500 hover:bg-green-600 text-white font-medium rounded-xl transition-all duration-200 flex items-center justify-center space-x-2"},c[8]||(c[8]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1),e("span",null,"已安装",-1)]))):(t(),l("button",{key:0,onClick:c[1]||(c[1]=Q(f=>a.$emit("purchase",a.app),["stop"])),class:"w-full py-3 px-4 bg-gradient-to-r from-primary-500 to-blue-500 hover:from-primary-600 hover:to-blue-600 text-white font-medium rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"},n(i()),1)),e("div",xt,[e("button",{onClick:c[3]||(c[3]=Q(f=>a.$emit("click",a.app),["stop"])),class:"flex-1 py-2 px-3 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-primary-600 dark:hover:text-primary-400 bg-gray-100 dark:bg-dark-card hover:bg-gray-200 dark:hover:bg-dark-border rounded-lg transition-all duration-200"}," 详情 "),e("button",{onClick:c[4]||(c[4]=Q(f=>a.$emit("favorite-toggle",a.app.id),["stop"])),class:j(["px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200",a.app.isFavorited?"text-red-500 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30":"text-gray-500 dark:text-dark-text-secondary bg-gray-100 dark:bg-dark-card hover:bg-gray-200 dark:hover:bg-dark-border hover:text-red-500"])},[g(I(ce),{class:j(["w-4 h-4",a.app.isFavorited?"fill-current":""])},null,8,["class"])],2)])])]),c[10]||(c[10]=e("div",{class:"absolute inset-0 bg-gradient-to-t from-primary-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none rounded-2xl"},null,-1))])}}}),wt=Z(bt,[["__scopeId","data-v-2764c877"]]),_t={class:"app-grid-container"},$t={key:0,class:"loading-container"},Ct={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"},At={key:1,class:"apps-container"},Vt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},It={key:0,class:"pagination-container mt-8 flex justify-center"},St={key:2,class:"empty-state"},Mt={class:"text-center py-16"},Nt={class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-2"},Tt={class:"text-gray-500 dark:text-dark-text-secondary max-w-md mx-auto"},Dt={class:"mt-6"},Ot=K({__name:"AppGrid",props:{apps:{},loading:{type:Boolean},emptyTitle:{},emptyDescription:{}},emits:["app-click","favorite-toggle","install-toggle","purchase","clear-filters"],setup(o){const u=o,i=h(1),s=h(12),a=$(()=>Math.ceil(u.apps.length/s.value)),c=$(()=>{const b=(i.value-1)*s.value,x=b+s.value;return u.apps.slice(b,x)}),V=$(()=>u.emptyTitle||"暂无应用"),f=$(()=>u.emptyDescription||"当前筛选条件下没有找到相关应用，请尝试调整筛选条件或搜索关键词。"),O=b=>{s.value=b,i.value=1},U=b=>{i.value=b,window.scrollTo({top:0,behavior:"smooth"})};return Ce(()=>u.apps.length,()=>{i.value=1}),(b,x)=>{const F=M("el-pagination"),R=M("el-button"),y=M("el-backtop");return t(),l("div",_t,[b.loading?(t(),l("div",$t,[e("div",Ct,[(t(),l(N,null,T(8,d=>e("div",{key:d,class:"app-card-skeleton bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden"},x[6]||(x[6]=[pe('<div class="p-6 space-y-4" data-v-ff91018e><div class="flex items-start space-x-4" data-v-ff91018e><div class="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-xl animate-pulse" data-v-ff91018e></div><div class="flex-1 space-y-2" data-v-ff91018e><div class="h-5 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" data-v-ff91018e></div><div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 animate-pulse" data-v-ff91018e></div></div></div><div class="space-y-2" data-v-ff91018e><div class="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" data-v-ff91018e></div><div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6 animate-pulse" data-v-ff91018e></div></div><div class="flex space-x-2" data-v-ff91018e><div class="h-6 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" data-v-ff91018e></div><div class="h-6 w-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" data-v-ff91018e></div></div></div><div class="px-6 py-4 bg-gray-50 dark:bg-dark-card/50 border-t border-gray-100 dark:border-dark-border" data-v-ff91018e><div class="flex items-center justify-between" data-v-ff91018e><div class="h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" data-v-ff91018e></div><div class="h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" data-v-ff91018e></div></div></div>',2)]))),64))])])):b.apps.length>0?(t(),l("div",At,[e("div",Vt,[(t(!0),l(N,null,T(c.value,d=>(t(),S(wt,{key:d.id,app:d,onClick:r=>b.$emit("app-click",d),onFavoriteToggle:x[0]||(x[0]=r=>b.$emit("favorite-toggle",r)),onInstallToggle:x[1]||(x[1]=r=>b.$emit("install-toggle",r)),onPurchase:x[2]||(x[2]=r=>b.$emit("purchase",r))},null,8,["app","onClick"]))),128))]),a.value>1?(t(),l("div",It,[g(F,{"current-page":i.value,"onUpdate:currentPage":x[3]||(x[3]=d=>i.value=d),"page-size":s.value,"onUpdate:pageSize":x[4]||(x[4]=d=>s.value=d),"page-sizes":[12,24,36,48],total:b.apps.length,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:O,onCurrentChange:U,class:"modern-pagination"},null,8,["current-page","page-size","total"])])):C("",!0)])):(t(),l("div",St,[e("div",Mt,[x[8]||(x[8]=e("div",{class:"w-24 h-24 mx-auto mb-6 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center"},[e("svg",{class:"w-12 h-12 text-gray-400 dark:text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5"})])],-1)),e("h3",Nt,n(V.value),1),e("p",Tt,n(f.value),1),e("div",Dt,[g(R,{onClick:x[5]||(x[5]=d=>b.$emit("clear-filters")),type:"primary",plain:""},{default:k(()=>x[7]||(x[7]=[A(" 清除筛选条件 ")])),_:1,__:[7]})])])])),g(y,{right:40,bottom:40,"visibility-height":300})])}}}),ye=Z(Ot,[["__scopeId","data-v-ff91018e"]]),jt={key:0,class:"purchase-content"},zt={class:"app-info flex items-center space-x-4 mb-6 p-4 bg-gray-50 dark:bg-dark-card rounded-lg"},Ut={class:"w-16 h-16 bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-900/30 dark:to-primary-800/30 rounded-xl flex items-center justify-center text-2xl font-bold border border-primary-200 dark:border-primary-700"},Ft={class:"flex-1"},Et={class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},Pt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Bt={class:"price-section mb-6"},Lt={class:"bg-white dark:bg-dark-surface border border-gray-200 dark:border-dark-border rounded-lg p-4"},Rt={key:0,class:"mt-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg"},Yt={class:"flex items-center space-x-2"},Gt={key:1,class:"mt-4 space-y-2"},Ht={class:"flex items-center justify-between text-sm"},Jt={class:"font-medium text-gray-900 dark:text-dark-text"},qt={key:0,class:"flex items-center justify-between text-sm"},Kt={class:"text-gray-400 line-through"},Zt={class:"flex items-center justify-between text-sm font-medium border-t border-gray-200 dark:border-dark-border pt-2"},Wt={class:"text-lg text-primary-600 dark:text-primary-400"},Qt={key:2,class:"mt-4 space-y-3"},Xt={class:"p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg"},ea={class:"flex items-center space-x-2 mb-2"},ta={class:"flex items-center justify-between text-sm font-medium border-t border-gray-200 dark:border-dark-border pt-2"},aa={class:"text-lg text-primary-600 dark:text-primary-400"},sa={key:3,class:"mt-4 space-y-3"},ra={class:"p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg"},oa={class:"flex items-center space-x-2 mb-2"},na={class:"space-y-2"},la={class:"flex items-center justify-between text-sm"},ia={class:"font-medium text-gray-900 dark:text-dark-text"},da={class:"flex items-center justify-between text-sm"},pa={class:"flex items-center justify-between text-sm"},ca={class:"font-medium text-gray-900 dark:text-dark-text"},ua={key:0,class:"payment-section mb-6"},ma={class:"space-y-2"},ga=["value"],va={class:"flex items-center space-x-3 flex-1"},ya={class:"text-2xl"},fa={class:"font-medium text-gray-900 dark:text-dark-text"},ka={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},ha={key:0,class:"text-primary-600 dark:text-primary-400"},xa={key:1,class:"agreement-section mb-6"},ba={class:"flex items-start space-x-3 cursor-pointer"},wa={class:"flex justify-end space-x-3"},_a=K({__name:"PurchaseDialog",props:{modelValue:{type:Boolean},app:{}},emits:["update:modelValue","purchase-success"],setup(o,{emit:u}){const i=o,s=u,a=h(!1),c=h("alipay"),V=h(!1),f=h(50),O=$({get:()=>i.modelValue,set:y=>s("update:modelValue",y)}),U=$(()=>i.app?i.app.price.type==="free"?!0:V.value&&c.value:!1),b=h([{id:"alipay",name:"支付宝",icon:"💙",description:"推荐使用，安全快捷"},{id:"wechat",name:"微信支付",icon:"💚",description:"微信扫码支付"},{id:"unionpay",name:"银联支付",icon:"💳",description:"银行卡支付"}]),x=()=>{if(!i.app)return"购买应用";switch(i.app.price.type){case"free":return"安装免费应用";case"one_time":return"购买应用";case"monthly":return"订阅应用";case"per_use":return"充值使用";default:return"购买应用"}},F=()=>{if(!i.app)return"确认";switch(i.app.price.type){case"free":return"立即安装";case"one_time":return`立即购买 ¥${i.app.price.amount}`;case"monthly":return`立即订阅 ¥${i.app.price.amount}/月`;case"per_use":return`充值 ¥${f.value}`;default:return"确认购买"}},R=async()=>{if(!(!i.app||!U.value)){a.value=!0;try{await new Promise(y=>setTimeout(y,2e3)),i.app.price.type==="free"?ee.success(`${i.app.name} 安装成功！`):ee.success(`${i.app.name} 购买成功！`),s("purchase-success",i.app.id),O.value=!1}catch{ee.error("操作失败，请重试")}finally{a.value=!1}}};return(y,d)=>{const r=M("el-input-number"),v=M("el-checkbox"),E=M("el-button"),Y=M("el-dialog");return t(),S(Y,{modelValue:O.value,"onUpdate:modelValue":d[4]||(d[4]=_=>O.value=_),title:x(),width:"500px","align-center":"","close-on-click-modal":!1,class:"purchase-dialog"},{footer:k(()=>[e("div",wa,[g(E,{onClick:d[3]||(d[3]=_=>O.value=!1),size:"large"},{default:k(()=>d[20]||(d[20]=[A(" 取消 ")])),_:1,__:[20]}),g(E,{onClick:R,type:"primary",size:"large",loading:a.value,disabled:!U.value},{default:k(()=>[A(n(F()),1)]),_:1},8,["loading","disabled"])])]),default:k(()=>[y.app?(t(),l("div",jt,[e("div",zt,[e("div",Ut,n(y.app.icon),1),e("div",Ft,[e("h3",Et,n(y.app.name),1),e("p",Pt,n(y.app.developer),1)])]),e("div",Bt,[d[17]||(d[17]=e("h4",{class:"text-base font-medium text-gray-900 dark:text-dark-text mb-3"}," 价格详情 ",-1)),e("div",Lt,[g(me,{price:y.app.price,size:"large"},null,8,["price"]),y.app.price.type==="free"?(t(),l("div",Rt,[e("div",Yt,[g(I(ve),{class:"w-5 h-5 text-green-600 dark:text-green-400"}),d[5]||(d[5]=e("span",{class:"text-sm text-green-800 dark:text-green-300"}," 此应用完全免费，无需付费即可使用所有功能 ",-1))])])):y.app.price.type==="one_time"?(t(),l("div",Gt,[e("div",Ht,[d[6]||(d[6]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"应用价格",-1)),e("span",Jt,"¥"+n(y.app.price.amount),1)]),y.app.price.originalAmount&&y.app.price.originalAmount>y.app.price.amount?(t(),l("div",qt,[d[7]||(d[7]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"原价",-1)),e("span",Kt,"¥"+n(y.app.price.originalAmount),1)])):C("",!0),e("div",Zt,[d[8]||(d[8]=e("span",{class:"text-gray-900 dark:text-dark-text"},"总计",-1)),e("span",Wt,"¥"+n(y.app.price.amount),1)])])):y.app.price.type==="monthly"?(t(),l("div",Qt,[e("div",Xt,[e("div",ea,[g(I(xe),{class:"w-5 h-5 text-blue-600 dark:text-blue-400"}),d[9]||(d[9]=e("span",{class:"text-sm font-medium text-blue-800 dark:text-blue-300"},"订阅说明",-1))]),d[10]||(d[10]=e("ul",{class:"text-sm text-blue-700 dark:text-blue-300 space-y-1"},[e("li",null,"• 按月自动续费，可随时取消"),e("li",null,"• 享受所有高级功能"),e("li",null,"• 优先技术支持"),e("li",null,"• 无使用次数限制")],-1))]),e("div",ta,[d[11]||(d[11]=e("span",{class:"text-gray-900 dark:text-dark-text"},"月费",-1)),e("span",aa,"¥"+n(y.app.price.amount)+"/月",1)])])):y.app.price.type==="per_use"?(t(),l("div",sa,[e("div",ra,[e("div",oa,[g(I(ze),{class:"w-5 h-5 text-orange-600 dark:text-orange-400"}),d[12]||(d[12]=e("span",{class:"text-sm font-medium text-orange-800 dark:text-orange-300"},"按需付费",-1))]),d[13]||(d[13]=e("p",{class:"text-sm text-orange-700 dark:text-orange-300"}," 根据实际使用次数计费，用多少付多少，经济实惠 ",-1))]),e("div",na,[e("div",la,[d[14]||(d[14]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"单次使用费用",-1)),e("span",ia,"¥"+n(y.app.price.amount),1)]),e("div",da,[d[15]||(d[15]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"预充值金额",-1)),g(r,{modelValue:f.value,"onUpdate:modelValue":d[0]||(d[0]=_=>f.value=_),min:10,max:1e3,step:10,size:"small",style:{width:"120px"}},null,8,["modelValue"])]),e("div",pa,[d[16]||(d[16]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"可使用次数",-1)),e("span",ca," 约 "+n(Math.floor(f.value/y.app.price.amount))+" 次 ",1)])])])):C("",!0)])]),y.app.price.type!=="free"?(t(),l("div",ua,[d[18]||(d[18]=e("h4",{class:"text-base font-medium text-gray-900 dark:text-dark-text mb-3"}," 支付方式 ",-1)),e("div",ma,[(t(!0),l(N,null,T(b.value,_=>(t(),l("label",{key:_.id,class:j(["flex items-center p-3 border border-gray-200 dark:border-dark-border rounded-lg cursor-pointer hover:border-primary-300 dark:hover:border-primary-600 transition-colors",c.value===_.id?"border-primary-500 bg-primary-50 dark:bg-primary-900/20":""])},[J(e("input",{"onUpdate:modelValue":d[1]||(d[1]=W=>c.value=W),value:_.id,type:"radio",class:"sr-only"},null,8,ga),[[Ae,c.value]]),e("div",va,[e("div",ya,n(_.icon),1),e("div",null,[e("div",fa,n(_.name),1),e("div",ka,n(_.description),1)])]),c.value===_.id?(t(),l("div",ha,[g(I(ve),{class:"w-5 h-5"})])):C("",!0)],2))),128))])])):C("",!0),y.app.price.type!=="free"?(t(),l("div",xa,[e("label",ba,[g(v,{modelValue:V.value,"onUpdate:modelValue":d[2]||(d[2]=_=>V.value=_)},null,8,["modelValue"]),d[19]||(d[19]=e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},[A(" 我已阅读并同意 "),e("a",{href:"#",class:"text-primary-600 dark:text-primary-400 hover:underline"},"《用户服务协议》"),A(" 和 "),e("a",{href:"#",class:"text-primary-600 dark:text-primary-400 hover:underline"},"《隐私政策》")],-1))])])):C("",!0)])):C("",!0)]),_:1},8,["modelValue","title"])}}}),be=Z(_a,[["__scopeId","data-v-2b0e2073"]]),$a={key:0,class:"app-details-content"},Ca={class:"app-header flex items-start space-x-6 mb-6"},Aa={class:"flex-shrink-0"},Va={class:"w-20 h-20 bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-900/30 dark:to-primary-800/30 rounded-2xl flex items-center justify-center text-3xl font-bold border border-primary-200 dark:border-primary-700"},Ia={class:"flex-1 min-w-0"},Sa={class:"flex items-center space-x-3 mb-2"},Ma={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},Na={class:"text-gray-600 dark:text-dark-text-secondary mb-3"},Ta={class:"flex items-center space-x-6 text-sm mb-4"},Da={class:"flex items-center space-x-2"},Oa={class:"flex items-center"},ja={class:"font-medium text-gray-900 dark:text-dark-text"},za={class:"text-gray-500 dark:text-dark-text-secondary"},Ua={class:"text-gray-500 dark:text-dark-text-secondary"},Fa={class:"text-gray-500 dark:text-dark-text-secondary"},Ea={class:"flex flex-wrap gap-2"},Pa={class:"flex-shrink-0 space-y-3"},Ba={class:"text-right"},La={class:"flex flex-col space-y-2"},Ra={class:"space-y-6"},Ya={class:"text-gray-600 dark:text-dark-text-secondary leading-relaxed"},Ga={key:0},Ha={class:"space-y-2"},Ja={class:"text-gray-600 dark:text-dark-text-secondary"},qa={key:1},Ka={class:"space-y-2"},Za={class:"text-gray-600 dark:text-dark-text-secondary"},Wa={key:0,class:"space-y-4"},Qa={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Xa=["onClick"],es=["src","alt"],ts={class:"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-200 rounded-lg flex items-center justify-center"},as={key:1,class:"text-center py-12"},ss={class:"space-y-6"},rs={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-6"},os={class:"flex items-center space-x-8"},ns={class:"text-center"},ls={class:"text-3xl font-bold text-gray-900 dark:text-dark-text"},is={class:"flex items-center justify-center mt-1"},ds={class:"text-sm text-gray-500 dark:text-dark-text-secondary mt-1"},ps={class:"flex-1 space-y-2"},cs={class:"text-sm text-gray-600 dark:text-dark-text-secondary w-8"},us={class:"flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2"},ms={class:"text-sm text-gray-500 dark:text-dark-text-secondary w-8"},gs={class:"space-y-4"},vs={class:"flex items-start space-x-4"},ys={class:"w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-medium"},fs={class:"flex-1"},ks={class:"flex items-center space-x-3 mb-2"},hs={class:"font-medium text-gray-900 dark:text-dark-text"},xs={class:"flex items-center"},bs={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},ws={class:"text-gray-600 dark:text-dark-text-secondary leading-relaxed"},_s=K({__name:"AppDetailsDialog",props:{modelValue:{type:Boolean},app:{}},emits:["update:modelValue","favorite-toggle","install-toggle"],setup(o,{emit:u}){const i=o,s=u,a=h("overview"),c=h(!1),V=h(0),f=h(!1),O=$({get:()=>i.modelValue,set:r=>s("update:modelValue",r)}),U=h([{id:1,user:"张三",rating:5,date:"2024-01-15",content:"非常好用的工具，界面简洁，功能强大，大大提高了工作效率！"},{id:2,user:"李四",rating:4,date:"2024-01-12",content:"整体不错，就是有些功能还需要完善，期待后续更新。"},{id:3,user:"王五",rating:5,date:"2024-01-10",content:"强烈推荐！客服响应也很及时，解决问题很专业。"}]),b=r=>r>=1e4?`${(r/1e4).toFixed(1)}万`:r>=1e3?`${(r/1e3).toFixed(1)}k`:r.toString(),x=r=>new Date(r).toLocaleDateString("zh-CN"),F=r=>({5:65,4:20,3:10,2:3,1:2})[r]||0,R=()=>{if(!i.app)return"立即安装";switch(i.app.price.type){case"free":return"立即安装";case"one_time":return`立即购买 ¥${i.app.price.amount}`;case"monthly":return`立即订阅 ¥${i.app.price.amount}/月`;case"per_use":return"立即充值";default:return"立即安装"}},y=(r,v)=>{V.value=v,c.value=!0},d=r=>{s("install-toggle",r),f.value=!1};return(r,v)=>{var ae;const E=M("el-tag"),Y=M("el-button"),_=M("el-tab-pane"),W=M("el-tabs"),ne=M("el-image-viewer"),le=M("el-dialog");return t(),S(le,{modelValue:O.value,"onUpdate:modelValue":v[6]||(v[6]=H=>O.value=H),title:((ae=r.app)==null?void 0:ae.name)||"应用详情",width:"900px","align-center":"","close-on-click-modal":!1,class:"app-details-dialog"},{default:k(()=>{var H;return[r.app?(t(),l("div",$a,[e("div",Ca,[e("div",Aa,[e("div",Va,n(r.app.icon),1)]),e("div",Ia,[e("div",Sa,[e("h2",Ma,n(r.app.name),1),r.app.status==="maintenance"?(t(),S(E,{key:0,type:"warning",effect:"plain"},{default:k(()=>v[7]||(v[7]=[A(" 维护中 ")])),_:1,__:[7]})):r.app.status==="deprecated"?(t(),S(E,{key:1,type:"danger",effect:"plain"},{default:k(()=>v[8]||(v[8]=[A(" 已废弃 ")])),_:1,__:[8]})):C("",!0)]),e("p",Na,n(r.app.developer)+" • v"+n(r.app.version),1),e("div",Ta,[e("div",Da,[e("div",Oa,[(t(),l(N,null,T(5,m=>g(I(oe),{key:m,class:j(["w-5 h-5",m<=Math.floor(r.app.rating)?"text-yellow-400 fill-current":"text-gray-300 dark:text-gray-600"])},null,8,["class"])),64))]),e("span",ja,n(r.app.rating),1),e("span",za," ("+n(r.app.reviewCount)+" 评价) ",1)]),e("div",Ua,n(b(r.app.downloadCount))+" 次使用 ",1),e("div",Fa," 更新于 "+n(x(r.app.lastUpdated)),1)]),e("div",Ea,[(t(!0),l(N,null,T(r.app.tags,m=>(t(),S(E,{key:m,size:"small",effect:"plain"},{default:k(()=>[A(n(m),1)]),_:2},1024))),128))])]),e("div",Pa,[e("div",Ba,[g(me,{price:r.app.price,size:"large"},null,8,["price"])]),e("div",La,[r.app.isInstalled?(t(),S(Y,{key:1,onClick:v[1]||(v[1]=m=>r.$emit("install-toggle",r.app.id)),type:"success",plain:"",size:"large",class:"w-full"},{default:k(()=>v[9]||(v[9]=[A(" 已安装 ")])),_:1,__:[9]})):(t(),S(Y,{key:0,onClick:v[0]||(v[0]=m=>f.value=!0),type:"primary",size:"large",class:"w-full"},{default:k(()=>[A(n(R()),1)]),_:1})),g(Y,{onClick:v[2]||(v[2]=m=>r.$emit("favorite-toggle",r.app.id)),type:r.app.isFavorited?"danger":"default",plain:!r.app.isFavorited,size:"large",class:"w-full"},{default:k(()=>[g(I(ce),{class:j(["w-4 h-4 mr-2",r.app.isFavorited?"fill-current":""])},null,8,["class"]),A(" "+n(r.app.isFavorited?"已收藏":"收藏"),1)]),_:1},8,["type","plain"])])])]),g(W,{modelValue:a.value,"onUpdate:modelValue":v[3]||(v[3]=m=>a.value=m),class:"app-details-tabs"},{default:k(()=>[g(_,{label:"应用介绍",name:"overview"},{default:k(()=>[e("div",Ra,[e("div",null,[v[10]||(v[10]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-3"}," 应用简介 ",-1)),e("p",Ya,n(r.app.longDescription||r.app.description),1)]),r.app.features&&r.app.features.length>0?(t(),l("div",Ga,[v[11]||(v[11]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-3"}," 主要功能 ",-1)),e("ul",Ha,[(t(!0),l(N,null,T(r.app.features,m=>(t(),l("li",{key:m,class:"flex items-center space-x-3"},[g(I(Te),{class:"w-5 h-5 text-green-500 flex-shrink-0"}),e("span",Ja,n(m),1)]))),128))])])):C("",!0),r.app.requirements&&r.app.requirements.length>0?(t(),l("div",qa,[v[12]||(v[12]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-3"}," 使用要求 ",-1)),e("ul",Ka,[(t(!0),l(N,null,T(r.app.requirements,m=>(t(),l("li",{key:m,class:"flex items-center space-x-3"},[g(I(xe),{class:"w-5 h-5 text-blue-500 flex-shrink-0"}),e("span",Za,n(m),1)]))),128))])])):C("",!0)])]),_:1}),g(_,{label:"应用截图",name:"screenshots"},{default:k(()=>[r.app.screenshots&&r.app.screenshots.length>0?(t(),l("div",Wa,[e("div",Qa,[(t(!0),l(N,null,T(r.app.screenshots,(m,P)=>(t(),l("div",{key:P,class:"relative group cursor-pointer",onClick:ie=>y(m,P)},[e("img",{src:m,alt:`应用截图 ${P+1}`,class:"w-full h-48 object-cover rounded-lg border border-gray-200 dark:border-dark-border group-hover:border-primary-300 dark:group-hover:border-primary-600 transition-colors duration-200"},null,8,es),e("div",ts,[g(I(Ue),{class:"w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200"})])],8,Xa))),128))])])):(t(),l("div",as,[g(I(Ve),{class:"w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4"}),v[13]||(v[13]=e("p",{class:"text-gray-500 dark:text-dark-text-secondary"},"暂无应用截图",-1))]))]),_:1}),g(_,{label:"用户评价",name:"reviews"},{default:k(()=>[e("div",ss,[e("div",rs,[e("div",os,[e("div",ns,[e("div",ls,n(r.app.rating),1),e("div",is,[(t(),l(N,null,T(5,m=>g(I(oe),{key:m,class:j(["w-4 h-4",m<=Math.floor(r.app.rating)?"text-yellow-400 fill-current":"text-gray-300 dark:text-gray-600"])},null,8,["class"])),64))]),e("div",ds,n(r.app.reviewCount)+" 条评价 ",1)]),e("div",ps,[(t(),l(N,null,T([5,4,3,2,1],m=>e("div",{key:m,class:"flex items-center space-x-3"},[e("span",cs,n(m)+"星 ",1),e("div",us,[e("div",{class:"bg-yellow-400 h-2 rounded-full",style:Ie({width:`${F(m)}%`})},null,4)]),e("span",ms,n(F(m))+"% ",1)])),64))])])]),e("div",gs,[(t(!0),l(N,null,T(U.value,m=>(t(),l("div",{key:m.id,class:"border border-gray-200 dark:border-dark-border rounded-lg p-4"},[e("div",vs,[e("div",ys,n(m.user.charAt(0)),1),e("div",fs,[e("div",ks,[e("span",hs,n(m.user),1),e("div",xs,[(t(),l(N,null,T(5,P=>g(I(oe),{key:P,class:j(["w-4 h-4",P<=m.rating?"text-yellow-400 fill-current":"text-gray-300 dark:text-gray-600"])},null,8,["class"])),64))]),e("span",bs,n(m.date),1)]),e("p",ws,n(m.content),1)])])]))),128))])])]),_:1})]),_:1},8,["modelValue"])])):C("",!0),c.value?(t(),S(ne,{key:1,"url-list":((H=r.app)==null?void 0:H.screenshots)||[],"initial-index":V.value,onClose:v[4]||(v[4]=m=>c.value=!1)},null,8,["url-list","initial-index"])):C("",!0),g(be,{modelValue:f.value,"onUpdate:modelValue":v[5]||(v[5]=m=>f.value=m),app:r.app,onPurchaseSuccess:d},null,8,["modelValue","app"])]}),_:1},8,["modelValue","title"])}}}),$s=Z(_s,[["__scopeId","data-v-5098ad69"]]);var X=(o=>(o.FREE="free",o.ONE_TIME="one_time",o.MONTHLY="monthly",o.PER_USE="per_use",o))(X||{}),B=(o=>(o.IMAGE_PROCESSING="image_processing",o.DATA_ANALYSIS="data_analysis",o.SEO_TOOLS="seo_tools",o.MARKET_ANALYSIS="market_analysis",o.MANAGEMENT_TOOLS="management_tools",o.AUTOMATION="automation",o.CONTENT_CREATION="content_creation",o))(B||{});const L=h([]),q=h([]),G=h([]),ue=h(!1),te=h({}),Cs=[{id:"product-collection",name:"商品采集",description:"智能采集全球电商平台商品信息，支持Amazon、Temu、Shein等主流平台",longDescription:"商品采集是一款专业的电商数据采集工具，支持从Amazon、Temu、Shein等主流电商平台批量采集商品信息。具备智能去重、数据清洗、格式转换等功能，是电商从业者的必备工具。",icon:"🛒",screenshots:["https://picsum.photos/800/600?random=1","https://picsum.photos/800/600?random=2","https://picsum.photos/800/600?random=3"],category:"data_analysis",tags:["数据采集","电商","批量处理","多平台"],price:{type:"monthly",amount:49.9,currency:"CNY",originalAmount:69.9,unit:"/月",description:"专业版功能，支持无限采集"},rating:4.9,reviewCount:2341,downloadCount:28750,developer:"RiinAI团队",version:"3.2.1",lastUpdated:"2024-01-16",status:"active",features:["多平台支持","智能去重","数据清洗","批量导出","定时采集"],requirements:["需要网络连接","支持Chrome浏览器插件"]},{id:"smart-crop",name:"智能裁图",description:"AI智能图片裁剪和优化，自动识别主体，支持多种裁剪比例",longDescription:"智能裁图是一款基于深度学习的图片裁剪工具，能够自动识别图片主体，进行智能裁剪。支持批量处理、多种裁剪比例、自定义裁剪区域等功能。",icon:"✂️",screenshots:["https://picsum.photos/800/600?random=4","https://picsum.photos/800/600?random=5","https://picsum.photos/800/600?random=6"],category:"image_processing",tags:["AI","图片处理","批量处理","智能裁剪"],price:{type:"monthly",amount:29.9,currency:"CNY",originalAmount:39.9,unit:"/月",description:"包含所有高级功能，无限制使用"},rating:4.8,reviewCount:1856,downloadCount:22420,developer:"RiinAI团队",version:"2.1.0",lastUpdated:"2024-01-15",status:"active",features:["智能主体识别","批量处理支持","多种裁剪比例","高质量输出","云端处理"],requirements:["需要网络连接","支持JPG/PNG格式"]},{id:"one-click-cutout",name:"一键抠图",description:"一键智能抠图，去除背景，AI驱动的背景移除工具",longDescription:"一键抠图使用先进的AI算法，能够精确识别图片主体，自动移除背景。支持批量处理，输出高质量透明背景图片，是设计师和电商从业者的得力助手。",icon:"🎨",screenshots:["https://picsum.photos/800/600?random=7","https://picsum.photos/800/600?random=8"],category:"image_processing",tags:["AI","抠图","背景移除","图片处理"],price:{type:"per_use",amount:.5,currency:"CNY",unit:"/张",description:"按使用次数计费，高质量处理"},rating:4.9,reviewCount:3241,downloadCount:45750,developer:"RiinAI团队",version:"1.8.0",lastUpdated:"2024-01-14",status:"active",features:["AI智能识别","高精度抠图","批量处理","多格式支持","云端处理"]},{id:"super-split",name:"超级裂变",description:"营销裂变工具，快速传播，支持多种裂变模式和数据分析",longDescription:"超级裂变是一款专业的营销裂变工具，支持多种裂变模式，包括分享裂变、任务裂变、拼团裂变等。提供详细的数据分析和用户行为追踪，帮助企业快速扩大用户规模。",icon:"🚀",screenshots:["https://picsum.photos/800/600?random=9","https://picsum.photos/800/600?random=10","https://picsum.photos/800/600?random=11"],category:"automation",tags:["营销","裂变","传播","数据分析"],price:{type:"monthly",amount:199,currency:"CNY",unit:"/月",description:"专业营销工具，支持无限裂变活动"},rating:4.7,reviewCount:1567,downloadCount:18920,developer:"RiinAI团队",version:"2.5.0",lastUpdated:"2024-01-13",status:"active",features:["多种裂变模式","数据分析","用户追踪","活动管理","效果统计"]},{id:"title-generator",name:"标题生成",description:"AI智能生成吸引人的标题，支持多种模板和平台优化",longDescription:"AI标题生成器基于大语言模型，能够为不同平台生成吸引人的标题。支持电商、自媒体、广告等多种场景，提供模板库和个性化定制功能。",icon:"📝",screenshots:["https://picsum.photos/800/600?random=12","https://picsum.photos/800/600?random=13"],category:"content_creation",tags:["AI","标题生成","内容创作","营销"],price:{type:"per_use",amount:.1,currency:"CNY",unit:"/次",description:"按生成次数计费，经济实惠"},rating:4.6,reviewCount:2890,downloadCount:35670,developer:"RiinAI团队",version:"1.9.2",lastUpdated:"2024-01-12",status:"active",features:["AI智能生成","多平台优化","模板库","批量生成","效果预测"]},{id:"pod-compose",name:"POD合成",description:"按需印刷商品合成工具，支持图案与产品的智能合成",longDescription:"POD合成工具专为按需印刷业务设计，支持将设计图案与各种产品模板进行智能合成。提供丰富的产品库、智能排版、批量处理等功能。",icon:"🎨",screenshots:["https://picsum.photos/800/600?random=14","https://picsum.photos/800/600?random=15","https://picsum.photos/800/600?random=16"],category:"image_processing",tags:["POD","合成","印刷","设计"],price:{type:"monthly",amount:89,currency:"CNY",unit:"/月",description:"专业POD工具，支持无限合成"},rating:4.5,reviewCount:1234,downloadCount:12450,developer:"RiinAI团队",version:"1.6.0",lastUpdated:"2024-01-11",status:"active",features:["智能合成","产品模板库","批量处理","高清输出","自动排版"]},{id:"batch-listing",name:"批量刊登",description:"批量发布商品到各大平台，支持模板导入和自动化发布",longDescription:"批量刊登工具支持将商品信息批量发布到Amazon、eBay、Shopify等多个电商平台。提供模板导入、数据映射、自动化发布等功能，大大提高运营效率。",icon:"📦",screenshots:["https://picsum.photos/800/600?random=17","https://picsum.photos/800/600?random=18"],category:"automation",tags:["批量发布","电商","自动化","多平台"],price:{type:"monthly",amount:159,currency:"CNY",unit:"/月",description:"专业电商工具，支持无限发布"},rating:4.4,reviewCount:987,downloadCount:15680,developer:"RiinAI团队",version:"2.3.1",lastUpdated:"2024-01-10",status:"active",features:["多平台支持","模板导入","批量发布","数据映射","发布监控"]},{id:"price-monitor",name:"价格监控",description:"实时监控商品价格变化，支持多平台价格对比和降价提醒",longDescription:"价格监控工具帮助您实时跟踪商品价格变化，支持Amazon、Temu、Shein等主流电商平台。提供价格历史图表、降价提醒、竞品对比等功能。",icon:"📊",screenshots:["https://picsum.photos/800/600?random=19","https://picsum.photos/800/600?random=20"],category:"data_analysis",tags:["价格监控","数据分析","电商","提醒"],price:{type:"free",amount:0,currency:"CNY",description:"免费版本，每日可监控10个商品"},rating:4.2,reviewCount:1892,downloadCount:28750,developer:"第三方开发者",version:"1.5.2",lastUpdated:"2024-01-10",status:"active",features:["多平台支持","价格历史图表","降价提醒","数据导出"],requirements:["需要网络连接"]},{id:"keyword-research",name:"关键词研究",description:"专业的关键词挖掘和分析工具，助力SEO优化",longDescription:"关键词研究专家是一款专业的SEO工具，提供关键词挖掘、竞争度分析、搜索量预测等功能。帮助您找到高价值的关键词，提升网站排名。",icon:"🔍",screenshots:["https://picsum.photos/800/600?random=21","https://picsum.photos/800/600?random=22","https://picsum.photos/800/600?random=23"],category:"seo_tools",tags:["SEO","关键词","搜索优化","竞争分析"],price:{type:"one_time",amount:199,currency:"CNY",originalAmount:299,description:"一次购买，终身使用"},rating:4.6,reviewCount:1567,downloadCount:13240,developer:"SEO专家团队",version:"3.0.1",lastUpdated:"2024-01-12",status:"active",features:["关键词挖掘","竞争度分析","搜索量预测","长尾词推荐","数据报告"]},{id:"review-analyzer",name:"评论分析",description:"智能分析商品评论情感，提取用户反馈洞察",longDescription:"评论分析工具使用自然语言处理技术，智能分析商品评论的情感倾向、关键词提取、用户满意度等。帮助商家了解产品优缺点，优化产品和服务。",icon:"💬",screenshots:["https://picsum.photos/800/600?random=24","https://picsum.photos/800/600?random=25"],category:"data_analysis",tags:["评论分析","NLP","情感分析","用户洞察"],price:{type:"monthly",amount:79,currency:"CNY",unit:"/月",description:"专业分析工具，无限制使用"},rating:4.3,reviewCount:756,downloadCount:9870,developer:"AI分析团队",version:"2.1.0",lastUpdated:"2024-01-09",status:"active",features:["情感分析","关键词提取","满意度评分","趋势分析","报告生成"]},{id:"competitor-analysis",name:"竞品分析",description:"深度分析竞争对手策略，洞察市场机会",longDescription:"竞品分析大师帮助您深入了解竞争对手的产品策略、价格策略、营销手段等。提供详细的分析报告和市场洞察，助力商业决策。",icon:"🎯",screenshots:["https://picsum.photos/800/600?random=26","https://picsum.photos/800/600?random=27"],category:"market_analysis",tags:["竞品分析","市场研究","策略分析","商业智能"],price:{type:"monthly",amount:299,currency:"CNY",unit:"/月",description:"专业版功能，深度分析报告"},rating:4.4,reviewCount:623,downloadCount:7890,developer:"商业分析专家",version:"2.3.0",lastUpdated:"2024-01-08",status:"active",features:["竞品监控","价格对比","营销策略分析","市场趋势预测","定制报告"]},{id:"inventory-management",name:"库存管理",description:"智能库存管理系统，支持多仓库、多渠道库存同步",longDescription:"智能库存管理系统提供全面的库存控制功能，支持多仓库管理、库存预警、自动补货、销售预测等。帮助企业优化库存结构，降低运营成本。",icon:"📋",screenshots:["https://picsum.photos/800/600?random=28","https://picsum.photos/800/600?random=29","https://picsum.photos/800/600?random=30"],category:"management_tools",tags:["库存管理","仓储","供应链","预测"],price:{type:"monthly",amount:399,currency:"CNY",unit:"/月",description:"企业级库存管理解决方案"},rating:4.5,reviewCount:445,downloadCount:5670,developer:"企业管理专家",version:"3.1.2",lastUpdated:"2024-01-07",status:"active",features:["多仓库管理","库存预警","自动补货","销售预测","报表分析"]},{id:"customer-service",name:"客服助手",description:"AI智能客服机器人，24小时自动回复客户咨询",longDescription:"AI客服助手基于大语言模型，能够理解客户问题并提供准确回复。支持多平台接入、知识库管理、人工客服转接等功能，大幅提升客服效率。",icon:"🎧",screenshots:["https://picsum.photos/800/600?random=31","https://picsum.photos/800/600?random=32"],category:"automation",tags:["AI客服","自动回复","知识库","多平台"],price:{type:"monthly",amount:199,currency:"CNY",unit:"/月",description:"AI客服解决方案，支持无限对话"},rating:4.6,reviewCount:1234,downloadCount:15670,developer:"AI服务团队",version:"2.0.5",lastUpdated:"2024-01-06",status:"active",features:["AI智能回复","多平台接入","知识库管理","人工转接","对话分析"]},{id:"data-export",name:"数据导出",description:"多格式数据导出工具，支持Excel、CSV、JSON等格式",longDescription:"数据导出工具支持将各种业务数据导出为多种格式，包括Excel、CSV、JSON、PDF等。提供数据清洗、格式转换、定时导出等功能。",icon:"📤",screenshots:["https://picsum.photos/800/600?random=33","https://picsum.photos/800/600?random=34"],category:"data_analysis",tags:["数据导出","格式转换","数据清洗","自动化"],price:{type:"free",amount:0,currency:"CNY",description:"免费工具，基础导出功能"},rating:4.1,reviewCount:567,downloadCount:12340,developer:"数据工具团队",version:"1.4.0",lastUpdated:"2024-01-05",status:"active",features:["多格式支持","数据清洗","批量导出","定时任务","模板定制"]},{id:"report-generator",name:"报表生成",description:"自动生成业务报表，支持多种图表和数据可视化",longDescription:"报表生成器能够自动收集业务数据，生成专业的分析报表。支持多种图表类型、数据可视化、定时发送等功能，让数据分析更简单。",icon:"📈",screenshots:["https://picsum.photos/800/600?random=35","https://picsum.photos/800/600?random=36","https://picsum.photos/800/600?random=37"],category:"data_analysis",tags:["报表生成","数据可视化","图表","自动化"],price:{type:"monthly",amount:129,currency:"CNY",unit:"/月",description:"专业报表工具，无限制生成"},rating:4.7,reviewCount:890,downloadCount:11230,developer:"数据分析专家",version:"2.2.1",lastUpdated:"2024-01-04",status:"active",features:["自动数据收集","多种图表","数据可视化","定时发送","模板库"]},{id:"workflow-automation",name:"工作流自动化",description:"无代码工作流自动化平台，连接各种应用和服务",longDescription:"工作流自动化平台让您无需编程即可创建复杂的自动化流程。支持连接数百种应用和服务，实现数据同步、任务自动化、通知提醒等功能。",icon:"⚡",screenshots:["https://picsum.photos/800/600?random=38","https://picsum.photos/800/600?random=39","https://picsum.photos/800/600?random=40"],category:"automation",tags:["工作流","自动化","无代码","集成"],price:{type:"monthly",amount:299,currency:"CNY",originalAmount:399,unit:"/月",description:"企业级自动化解决方案"},rating:4.8,reviewCount:1567,downloadCount:8900,developer:"自动化专家",version:"3.0.0",lastUpdated:"2024-01-03",status:"active",features:["无代码设计","应用集成","条件触发","数据转换","监控告警"]}],As=()=>{L.value=Cs.map(o=>({...o,isFavorited:q.value.includes(o.id),isInstalled:G.value.includes(o.id)}))},Vs=()=>L.value,fe=$(()=>L.value.filter(o=>o.isFavorited)),Is=$(()=>{let o=L.value;const u=te.value;if(u.searchKeyword){const i=u.searchKeyword.toLowerCase();o=o.filter(s=>s.name.toLowerCase().includes(i)||s.description.toLowerCase().includes(i)||s.tags.some(a=>a.toLowerCase().includes(i)))}return u.category&&(o=o.filter(i=>i.category===u.category)),u.priceType&&(o=o.filter(i=>i.price.type===u.priceType)),u.rating!==void 0&&(o=o.filter(i=>i.rating>=u.rating)),u.sortBy&&o.sort((i,s)=>{let a,c;switch(u.sortBy){case"name":a=i.name,c=s.name;break;case"rating":a=i.rating,c=s.rating;break;case"downloadCount":a=i.downloadCount,c=s.downloadCount;break;case"lastUpdated":a=new Date(i.lastUpdated),c=new Date(s.lastUpdated);break;case"price":a=i.price.amount,c=s.price.amount;break;default:return 0}return u.sortOrder==="desc"?a>c?-1:a<c?1:0:a<c?-1:a>c?1:0}),o}),Ss=o=>{te.value={...te.value,...o}},Ms=()=>{te.value={}},Ns=o=>{const u=L.value.find(s=>s.id===o);if(!u)return!1;const i=q.value.indexOf(o);return i>-1?(q.value.splice(i,1),u.isFavorited=!1):(q.value.push(o),u.isFavorited=!0),localStorage.setItem("app-market-favorites",JSON.stringify(q.value)),u.isFavorited},Ts=o=>{const u=L.value.find(s=>s.id===o);if(!u)return!1;const i=G.value.indexOf(o);return i>-1?(G.value.splice(i,1),u.isInstalled=!1):(G.value.push(o),u.isInstalled=!0),localStorage.setItem("app-market-installed",JSON.stringify(G.value)),u.isInstalled},ke=o=>L.value.find(u=>u.id===o),Ds=()=>{ue.value=!0;const o=localStorage.getItem("app-market-favorites");o&&(q.value=JSON.parse(o));const u=localStorage.getItem("app-market-installed");u&&(G.value=JSON.parse(u)),As(),ue.value=!1},he={apps:$(()=>L.value),installedApps:G,loading:$(()=>ue.value),currentFilter:$(()=>te.value)},Os={class:"space-y-6"},js={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},zs={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Us={class:"flex items-center justify-between"},Fs={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},Es={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Ps={class:"flex items-center justify-between"},Bs={class:"text-2xl font-bold text-green-600 dark:text-green-400"},Ls={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Rs={class:"flex items-center justify-between"},Ys={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},Gs={class:"bg-white dark:bg-dark-surface rounded-2xl p-8 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},Hs={class:"mb-6"},Js={class:"relative max-w-2xl mx-auto"},qs={class:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none"},Ks={class:"flex flex-wrap items-center justify-center gap-4"},Zs={class:"filter-group"},Ws=["value"],Qs={class:"filter-group"},Xs=["value"],er={class:"filter-group"},tr={class:"filter-group"},ar={class:"flex items-center space-x-2"},sr={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},rr={class:"flex items-center"},or=K({__name:"index",setup(o){const u=h("all"),i=h(""),s=h(""),a=h(""),c=h(""),V=h("name"),f=h("asc"),O=h(!1),U=h(null),b=h(!1),x=h(null),F=$(()=>he.loading.value),R=$(()=>Vs().length),y=$(()=>fe.value.length),d=$(()=>he.installedApps.value.length),r=$(()=>Is.value),v=$(()=>fe.value),E=$(()=>[{label:"图像处理",value:B.IMAGE_PROCESSING},{label:"数据分析",value:B.DATA_ANALYSIS},{label:"SEO工具",value:B.SEO_TOOLS},{label:"市场分析",value:B.MARKET_ANALYSIS},{label:"管理工具",value:B.MANAGEMENT_TOOLS},{label:"自动化工具",value:B.AUTOMATION},{label:"内容创作",value:B.CONTENT_CREATION}]),Y=$(()=>[{label:"免费",value:X.FREE},{label:"一口价",value:X.ONE_TIME},{label:"包月",value:X.MONTHLY},{label:"按次计费",value:X.PER_USE}]),_=()=>{m()},W=()=>{m()},ne=()=>{m()},le=()=>{m()},ae=()=>{m()},H=()=>{f.value=f.value==="asc"?"desc":"asc",m()},m=()=>{const D={searchKeyword:i.value||void 0,category:s.value||void 0,priceType:a.value||void 0,rating:c.value||void 0,sortBy:V.value,sortOrder:f.value};Ss(D)},P=()=>{i.value="",s.value="",a.value="",c.value="",V.value="name",f.value="asc",Ms()},ie=D=>{U.value=D,O.value=!0},de=D=>{const p=Ns(D),z=ke(D);z&&ee.success(p?`已收藏 ${z.name}`:`已取消收藏 ${z.name}`)},se=D=>{const p=Ts(D),z=ke(D);z&&ee.success(p?`已安装 ${z.name}`:`已卸载 ${z.name}`)},ge=D=>{x.value=D,b.value=!0},we=D=>{se(D),b.value=!1};return Se(()=>{Ds()}),(D,p)=>{const z=M("el-tab-pane"),_e=M("el-badge"),$e=M("el-tabs");return t(),l("div",Os,[p[25]||(p[25]=pe('<div class="bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 rounded-2xl p-6 border border-primary-100 dark:border-primary-800" data-v-fffcac30><div class="flex items-center space-x-3" data-v-fffcac30><div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center" data-v-fffcac30><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-fffcac30><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" data-v-fffcac30></path></svg></div><div data-v-fffcac30><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-fffcac30>应用市场</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-fffcac30>发现和管理您的应用工具</p></div></div></div>',1)),e("div",js,[e("div",zs,[e("div",Us,[e("div",null,[p[9]||(p[9]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"总应用数",-1)),e("p",Fs,n(R.value),1)]),p[10]||(p[10]=e("div",{class:"w-12 h-12 bg-primary-100 dark:bg-primary-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-primary-600 dark:text-primary-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})])],-1))])]),e("div",Es,[e("div",Ps,[e("div",null,[p[11]||(p[11]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"我的收藏",-1)),e("p",Bs,n(y.value),1)]),p[12]||(p[12]=e("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})])],-1))])]),e("div",Ls,[e("div",Rs,[e("div",null,[p[13]||(p[13]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"已安装",-1)),e("p",Ys,n(d.value),1)]),p[14]||(p[14]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])])]),e("div",Gs,[e("div",Hs,[e("div",Js,[e("div",qs,[g(I(De),{class:"w-5 h-5 text-gray-400"})]),J(e("input",{"onUpdate:modelValue":p[0]||(p[0]=w=>i.value=w),onInput:_,type:"text",placeholder:"搜索应用名称、描述或标签...",class:"w-full pl-12 pr-4 py-4 text-lg bg-gray-50 dark:bg-dark-card border border-gray-200 dark:border-dark-border rounded-2xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 placeholder-gray-400 dark:placeholder-gray-500 text-gray-900 dark:text-dark-text"},null,544),[[Me,i.value]]),i.value?(t(),l("button",{key:0,onClick:p[1]||(p[1]=w=>{i.value="",_()}),class:"absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},p[15]||(p[15]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))):C("",!0)])]),e("div",Ks,[e("div",Zs,[p[17]||(p[17]=e("label",{class:"filter-label"},"分类",-1)),J(e("select",{"onUpdate:modelValue":p[2]||(p[2]=w=>s.value=w),onChange:W,class:"filter-select"},[p[16]||(p[16]=e("option",{value:""},"全部分类",-1)),(t(!0),l(N,null,T(E.value,w=>(t(),l("option",{key:w.value,value:w.value},n(w.label),9,Ws))),128))],544),[[re,s.value]])]),e("div",Qs,[p[19]||(p[19]=e("label",{class:"filter-label"},"价格",-1)),J(e("select",{"onUpdate:modelValue":p[3]||(p[3]=w=>a.value=w),onChange:ne,class:"filter-select"},[p[18]||(p[18]=e("option",{value:""},"全部价格",-1)),(t(!0),l(N,null,T(Y.value,w=>(t(),l("option",{key:w.value,value:w.value},n(w.label),9,Xs))),128))],544),[[re,a.value]])]),e("div",er,[p[21]||(p[21]=e("label",{class:"filter-label"},"评分",-1)),J(e("select",{"onUpdate:modelValue":p[4]||(p[4]=w=>c.value=w),onChange:le,class:"filter-select"},p[20]||(p[20]=[e("option",{value:""},"全部评分",-1),e("option",{value:4},"4星以上",-1),e("option",{value:3},"3星以上",-1),e("option",{value:2},"2星以上",-1)]),544),[[re,c.value]])]),e("div",tr,[p[23]||(p[23]=e("label",{class:"filter-label"},"排序",-1)),e("div",ar,[J(e("select",{"onUpdate:modelValue":p[5]||(p[5]=w=>V.value=w),onChange:ae,class:"filter-select"},p[22]||(p[22]=[pe('<option value="name" data-v-fffcac30>按名称</option><option value="rating" data-v-fffcac30>按评分</option><option value="downloadCount" data-v-fffcac30>按下载量</option><option value="lastUpdated" data-v-fffcac30>按更新时间</option><option value="price" data-v-fffcac30>按价格</option>',5)]),544),[[re,V.value]]),e("button",{onClick:H,class:"p-2 rounded-lg bg-gray-100 dark:bg-dark-card hover:bg-gray-200 dark:hover:bg-dark-border transition-colors duration-200"},[(t(),S(Ne(f.value==="asc"?I(je):I(Oe)),{class:"w-4 h-4 text-gray-600 dark:text-dark-text-secondary"}))])])]),e("button",{onClick:P,class:"px-4 py-2 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-primary-600 dark:hover:text-primary-400 bg-gray-100 dark:bg-dark-card hover:bg-gray-200 dark:hover:bg-dark-border rounded-lg transition-all duration-200"}," 清除筛选 ")])]),e("div",sr,[g($e,{modelValue:u.value,"onUpdate:modelValue":p[6]||(p[6]=w=>u.value=w),class:"app-market-tabs"},{default:k(()=>[g(z,{label:"全部应用",name:"all"},{default:k(()=>[g(ye,{apps:r.value,loading:F.value,onAppClick:ie,onFavoriteToggle:de,onInstallToggle:se,onPurchase:ge},null,8,["apps","loading"])]),_:1}),g(z,{name:"favorites"},{label:k(()=>[e("span",rr,[p[24]||(p[24]=A(" 我的收藏 ")),g(_e,{value:y.value,hidden:y.value===0,class:"ml-2"},null,8,["value","hidden"])])]),default:k(()=>[g(ye,{apps:v.value,loading:F.value,onAppClick:ie,onFavoriteToggle:de,onInstallToggle:se,onPurchase:ge},null,8,["apps","loading"])]),_:1})]),_:1},8,["modelValue"])]),g($s,{modelValue:O.value,"onUpdate:modelValue":p[7]||(p[7]=w=>O.value=w),app:U.value,onFavoriteToggle:de,onInstallToggle:se},null,8,["modelValue","app"]),g(be,{modelValue:b.value,"onUpdate:modelValue":p[8]||(p[8]=w=>b.value=w),app:x.value,onPurchaseSuccess:we},null,8,["modelValue","app"])])}}}),cr=Z(or,[["__scopeId","data-v-fffcac30"]]);export{cr as default};
